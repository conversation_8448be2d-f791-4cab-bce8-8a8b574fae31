﻿using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Events.Arguments.Scp914Events;
using LabApi.Features.Wrappers;
using LightContainmentZoneDecontamination;
using MEC;
using PlayerRoles;
using PlayerStatsSystem;
using Log = LabApi.Features.Console.Logger;
using Scp914;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using static TheRiptide.Translation;
using LabApi.Events.Arguments.ServerEvents;
using System.Reflection;
using CommandSystem;

namespace TheRiptide
{
    public class DmRound : IFeature
    {
        public static DmRound Singleton { get; private set; }

        private MainConfig config;

        private static bool game_started = false;
        public static bool game_ended = false;
        public static SortedSet<int> players = new SortedSet<int>();
        public Action OnConfigReloaded;
        private CoroutineHandle restart_handle;
        private CoroutineHandle round_timer_handle;
        private CoroutineHandle round_5_minute_warning;
        private CoroutineHandle round_1_minute_warning;

        public static bool GameStarted
        {
            get => game_started;
            set
            {
                if (value == true)
                {
                    foreach (var player in Player.List)
                        if (player.IsAlive)
                            Killstreaks.Singleton.AddKillstreakStartEffects(player);
                }
                else
                {
                    foreach (var player in Player.List)
                        if (player.IsAlive)
                            Lobby.ApplyGameNotStartedEffects(player);
                }
                game_started = value;
            }
        }

        public DmRound()
        {
            Singleton = this;
            Killfeeds.Init(2, 5, 20);

            OnConfigReloaded = new Action(() =>
            {
                Timing.CallDelayed(3.0f, () =>
                {
                    try
                    {
                        // 检查是否启用了服务器配置修改
                        if (Deathmatch.Singleton?.Config?.EnableServerConfigModifications == true)
                        {
                            Log.Info("[LAB API] Applying server configuration modifications...");
                            ServerConsole.FriendlyFire = true;
                            FriendlyFireConfig.PauseDetector = true;
                            Log.Info("[LAB API] Server configuration modifications applied successfully.");
                        }
                        else
                        {
                            Log.Info("[LAB API] Server configuration modifications are disabled.");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error("[LAB API] Failed to apply server configuration modifications. This may indicate a Remote Admin config issue: " + ex.ToString());
                    }
                });
            });
            LabApi.Events.Handlers.PlayerEvents.Joined += OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left += OnPlayerLeft;
            LabApi.Events.Handlers.Scp914Events.Activating += OnScp914Activate;
            LabApi.Events.Handlers.ServerEvents.RoundStarted += OnRoundStart;
            LabApi.Events.Handlers.ServerEvents.RoundEnded += RoundEnd;
            LabApi.Events.Handlers.ServerEvents.RoundRestarted += RoundRestart;
        }
        public void Stop()
        {
            LabApi.Events.Handlers.PlayerEvents.Joined -= OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left -= OnPlayerLeft;
            LabApi.Events.Handlers.Scp914Events.Activating -= OnScp914Activate;
            LabApi.Events.Handlers.ServerEvents.RoundStarted -= OnRoundStart;
            LabApi.Events.Handlers.ServerEvents.RoundEnded -= RoundEnd;
            LabApi.Events.Handlers.ServerEvents.RoundRestarted -= RoundRestart;
        }

        public void Init(MainConfig config)
        {
            this.config = config;
        }

        public void WaitingForPlayers()
        {
            game_ended = false;
            Database.Singleton.Checkpoint();
        }

        void OnRoundStart()
        {
            // 安全地设置友军伤害倍数
            try
            {
                var ffMultiplierField = typeof(AttackerDamageHandler).GetField("_ffMultiplier",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                if (ffMultiplierField != null)
                {
                    ffMultiplierField.SetValue(null, 1.0f);
                    Log.Debug("[LAB API] Successfully set friendly fire multiplier to 1.0 in OnRoundStart");
                }
                else
                {
                    Log.Warn("[LAB API] Could not find _ffMultiplier field in OnRoundStart, friendly fire multiplier not set");
                }
            }
            catch (Exception ex)
            {
                Log.Error("[LAB API] Failed to set friendly fire multiplier in OnRoundStart: " + ex.ToString());
            }
            if (config.RoundTime > 5.0f)
                round_5_minute_warning = Timing.CallDelayed(60.0f * (config.RoundTime - 5.0f), () => { BroadcastOverride.BroadcastLine(1, 30, BroadcastPriority.Medium, translation.RoundEnd5Minutes ); });
            if (config.RoundTime > 1.0f)
                round_1_minute_warning = Timing.CallDelayed(60.0f * (config.RoundTime - 1.0f), () => { BroadcastOverride.BroadcastLine(1, 30, BroadcastPriority.Medium, translation.RoundEnd1Minute ); });
            round_timer_handle = Timing.CallDelayed(60.0f * config.RoundTime, () =>
            {
                EndGame();
            });

            Log.Info($"Server.Instance.IpAddress: {Server.Host.IpAddress == null}");
            Server.Host.SetRole(RoleTypeId.Scp939);

            // 安全地设置服务器主机显示名称
            try
            {
                Server.Host.DisplayName = config.DummyPlayerName;
                Log.Debug("[LAB API] Successfully set server host display name");
            }
            catch (Exception ex)
            {
                Log.Error("[LAB API] Failed to set server host display name: " + ex.ToString());
            }

            Server.Host.Position = new Vector3(128.8f, 294.0f, 18.0f);
            Round.IsLocked = true;
            Warhead.IsLocked = true;
            DecontaminationController.Singleton.Network_decontaminationOverride = DecontaminationController.DecontaminationStatus.Disabled;
        }
        [CommandHandler(typeof(RemoteAdminCommandHandler))]
        public class GE : ICommand
        {
            public string Command => "dm_round_end";

            public string[] Aliases => [];

            public string Description => "Ends the current round and starts a new one.";

            public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
            {
                DmRound.Singleton.EndGame();
                response = "Ending the current round and starting a new one...";
                return true;
            }
        }

        public void EndGame()
        {
            try
            {
                restart_handle = Timing.CallDelayed(config.RoundEndTime, () => Round.Restart(false));
                Timing.CallPeriodically(config.RoundEndTime, 0.2f, () =>
                {
                    foreach (var p in Player.List)
                        p.IsGodModeEnabled = true;
                });
                try { Statistics.DisplayRoundStats(); }
                catch (Exception ex) { Log.Error(ex.ToString()); }
                try { Experiences.Singleton.SaveExperiences(); }
                catch (Exception ex) { Log.Error(ex.ToString()); }
                try { Ranks.Singleton.CalculateAndSaveRanks(); }
                catch (Exception ex) { Log.Error(ex.ToString()); }
                HintOverride.Refresh();
                VoiceChat.Singleton.ForceGlobalTalkGlobalReceive();
                Server.Host.SetRole(RoleTypeId.Spectator);
                game_ended = true;
                Tracking.Singleton.UpdateLeaderBoard();
                LeaderBoard.Singleton.ReloadLeaderBoard();
                if (Deathmatch.Singleton.Config.global_reference_config.leader_board_config.DisplayEndRoundDelay < config.RoundEndTime)
                {
                    LeaderBoard.Singleton.EnableTitle = false;
                    Timing.CallDelayed(Deathmatch.Singleton.Config.global_reference_config.leader_board_config.DisplayEndRoundDelay, () =>
                    {
                        foreach (var p in Player.List)
                            if (p.IsReady)
                                LeaderBoard.Singleton.EnableLeaderBoardMode(p, Enum.IsDefined(typeof(LeaderBoardType), Deathmatch.Singleton.Config.global_reference_config.leader_board_config.LeaderBoardType) ? (LeaderBoardType)Deathmatch.Singleton.Config.global_reference_config.leader_board_config.LeaderBoardType : (LeaderBoardType)UnityEngine.Random.Range(0, Enum.GetValues(typeof(LeaderBoardType)).Length));
                    });
                }
            }
            catch (Exception ex)
            {
                Log.Error("round end Error: " + ex.ToString());
            }
        }
        void OnPlayerJoined(PlayerJoinedEventArgs ev)
        {
            players.Add(ev.Player.PlayerId);
            if (!ev.Player.DoNotTrack)
                Database.Singleton.LoadConfig(ev.Player);
            else
                Timing.CallDelayed(1.0f, () => { HintOverride.Add(ev.Player, 0, translation.DntMsg, 30.0f); HintOverride.Refresh(ev.Player); });
        }

        void OnPlayerLeft(PlayerLeftEventArgs ev)
        {
            players.Remove(ev.Player.PlayerId);
        }

        void OnScp914Activate(Scp914ActivatingEventArgs ev)
        {
            ev.IsAllowed = false;
        }


        public void RoundRestart()
        {
            Timing.KillCoroutines(round_timer_handle, restart_handle, round_5_minute_warning, round_1_minute_warning);
            Round.IsLocked = false;
        }

        public void RoundEnd(RoundEndedEventArgs ev)
        {
            Timing.KillCoroutines(round_timer_handle, restart_handle, round_5_minute_warning, round_1_minute_warning);
            Round.IsLocked = false;
        }
    }
}
