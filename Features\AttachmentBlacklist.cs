﻿using InventorySystem.Items;
using InventorySystem.Items.Firearms;
using InventorySystem.Items.Firearms.Attachments;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Features.Wrappers;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using static TheRiptide.Translation;
using Log = LabApi.Features.Console.Logger;

namespace TheRiptide
{
    //DOUBLE-SHOT SYSTEM
    public class AttachmentBlacklistConfig
    {
        public bool IsEnabled { get; set; } = true;

        [Description("put black listed attachments here, see global reference config for attachment types")]
        public List<AttachmentName> BlackList { get; set; } = new List<AttachmentName>();
    }

    class AttachmentBlacklist : IFeature
    {
        public static AttachmentBlacklist Singleton { get; private set; }
        private Dictionary<ItemType, uint> BannedWeaponCodes = new Dictionary<ItemType,  uint>();

        AttachmentBlacklistConfig config;

        public AttachmentBlacklist()
        {
            Singleton = this;
            LabApi.Events.Handlers.ServerEvents.RoundStarted += OnRoundStart;
            LabApi.Events.Handlers.PlayerEvents.ChangedItem += OnPlayerChangeItem;
            LabApi.Events.Handlers.PlayerEvents.ShotWeapon += OnShotWeapon;

        }
        public void Stop()
        {
            LabApi.Events.Handlers.ServerEvents.RoundStarted -= OnRoundStart;
            LabApi.Events.Handlers.PlayerEvents.ChangedItem -= OnPlayerChangeItem;
            LabApi.Events.Handlers.PlayerEvents.ShotWeapon -= OnShotWeapon;
        }
        public void Init(AttachmentBlacklistConfig config)
        {
            this.config = config;
        }

        void OnRoundStart()
        {
            //foreach(RoomIdentifier room in RoomIdentifier.AllRoomIdentifiers)
            //{
            //    WorkstationController wc = room.GetComponentInChildren<WorkstationController>();
            //    if (wc != null)
            //    NetworkServer.UnSpawn(wc.gameObject);
            //}
        }
        void OnPlayerChangeItem(PlayerChangedItemEventArgs ev)
        {
            if (ev.Player.Items.Contains(ev.NewItem))
            {
                ItemBase item = ev.Player.ReferenceHub.inventory.UserInventory.Items.Values.First(x => x == ev.NewItem.Base);
                if (item is Firearm firearm)
                    RemoveBanned(ev.Player, firearm);
            }
        }

        void OnShotWeapon(PlayerShotWeaponEventArgs ev)
        {
            RemoveBanned(ev.Player, ev.FirearmItem.Base);
        }

        private void RemoveBanned(Player player, Firearm firearm)
        {
            if(!BannedWeaponCodes.ContainsKey(firearm.ItemTypeId))
            {
                int bit_pos = 0;
                uint code_mask = 0;
                foreach (var a in firearm.Attachments)
                {
                    if(config.BlackList.Contains(a.Name))
                        code_mask |= (1U << bit_pos);
                    bit_pos++;
                }
                BannedWeaponCodes.Add(firearm.ItemTypeId, ~code_mask);
            }

            uint old_code = firearm.GetCurrentAttachmentsCode();
            if (!BannedWeaponCodes.ContainsKey(firearm.ItemTypeId))
            {
                Log.Warn($"[LAB API] Weapon {firearm.ItemTypeId} not found in BannedWeaponCodes dictionary");
                return;
            }

            uint new_code = old_code & BannedWeaponCodes[firearm.ItemTypeId];
            if(new_code != old_code)
            {
                BitArray ba = new BitArray(BitConverter.GetBytes(~BannedWeaponCodes[firearm.ItemTypeId]));
                List<string> attachments = new List<string>();
                int index = 0;
                foreach(bool b in ba)
                {
                    if (b)
                        attachments.Add(firearm.Attachments[index].Name.ToString());
                    index++;
                    if (index >= firearm.Attachments.Length)
                        break;
                }
                BroadcastOverride.BroadcastLine(player, 1, 3.0f, BroadcastPriority.Medium, translation.AttachmentBanned.Replace("{attachment}", string.Join(", ", attachments)));
                BroadcastOverride.UpdateIfDirty(player);
                firearm.ApplyAttachmentsCode(new_code, true);
            }
        }
    }
}
