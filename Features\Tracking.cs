﻿using InventorySystem.Items.Firearms.Attachments;
using InventorySystem.Items.Firearms;
using PlayerStatsSystem;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static TheRiptide.Utility;
using UnityEngine;
using CommandSystem;
using RemoteAdmin;
using LabApi.Features.Wrappers;
using Log = LabApi.Features.Console.Logger;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Events.Arguments.ServerEvents;
using LabApi.Events;
namespace TheRiptide
{
    public class TrackingConfig
    {
        public bool IsEnabled { get; set; } = true;
        public bool TrackHits { get; set; } = true;
        public bool TrackLoadouts { get; set; } = true;
        public bool TrackRounds { get; set; } = true;

        public List<PlayerPermissions> TrackingCmdPermissions { get; set; } = new List<PlayerPermissions>
        {
            PlayerPermissions.ServerConsoleCommands
        };
    }

    public class Tracking : IFeature
    {
        public static Tracking Singleton { get; private set; }
        private TrackingConfig config;

        private Database.Round current_round = new Database.Round();
        private Dictionary<int, Database.Session> player_sessions = new Dictionary<int, Database.Session>();
        private Dictionary<int, Database.Life> player_life = new Dictionary<int, Database.Life>();
        private LabEventHandler<PlayerHurtEventArgs> OnPlayerDamaged;

        public Tracking()
        {
            Singleton = this;
            LabApi.Events.Handlers.PlayerEvents.ShotWeapon += OnPlayerShotWeapon;
            LabApi.Events.Handlers.PlayerEvents.Dying += OnPlayerDeath;
            LabApi.Events.Handlers.PlayerEvents.Hurting += OnPlayerDamage;
            LabApi.Events.Handlers.ServerEvents.WaitingForPlayers += WaitingForPlayers;
            LabApi.Events.Handlers.ServerEvents.RoundStarting += OnRoundStart;
            LabApi.Events.Handlers.PlayerEvents.Joined += OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left += OnPlayerLeft;
        }
        public void Stop()
        {
            LabApi.Events.Handlers.PlayerEvents.ShotWeapon -= OnPlayerShotWeapon;
            LabApi.Events.Handlers.PlayerEvents.Dying -= OnPlayerDeath;
            LabApi.Events.Handlers.PlayerEvents.Hurting -= OnPlayerDamage;
            LabApi.Events.Handlers.ServerEvents.WaitingForPlayers -= WaitingForPlayers;
            LabApi.Events.Handlers.ServerEvents.RoundStarting -= OnRoundStart;
            LabApi.Events.Handlers.PlayerEvents.Joined -= OnPlayerJoined;
        }
        public void Init(TrackingConfig config)
        {
            this.config = config;
            if (config.TrackHits)
            {
                OnPlayerDamaged = new LabEventHandler<PlayerHurtEventArgs>((e) =>
                {
                    try
                    {
                        if (e.Player.ReferenceHub != null)
                        {
                            if (e.DamageHandler is AttackerDamageHandler attacker && player_life.ContainsKey(e.Player.ReferenceHub.PlayerId) && player_life.ContainsKey(attacker.Attacker.PlayerId) && !player_life[e.Player.ReferenceHub.PlayerId].received.IsEmpty())
                                player_life[e.Player.ReferenceHub.PlayerId].received.Last().damage = (byte)Mathf.Clamp(Mathf.RoundToInt(attacker.DealtHealthDamage), 0, 255);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error("on damage error: " + ex.ToString());
                    }
                });
                LabApi.Events.Handlers.PlayerEvents.Hurt += OnPlayerDamaged;
            }
        }

        public void WaitingForPlayers()
        {
            current_round = null;
        }
        void OnRoundStart(RoundStartingEventArgs ev)
        {
            if (config.TrackRounds)
            {
                current_round = new Database.Round();
                foreach (var ids in player_sessions.Keys.ToList())
                    player_sessions[ids].round = current_round;
            }
        }

        void OnPlayerJoined(PlayerJoinedEventArgs ev)
        {
            int id = ev.Player.PlayerId;
            if (!player_sessions.ContainsKey(id))
                player_sessions.Add(id, new Database.Session());
            else
                player_sessions[ev.Player.PlayerId] = new Database.Session();

            Database.Session session = player_sessions[ev.Player.PlayerId];
            session.nickname = ev.Player.Nickname;
            session.round = current_round;

            if (current_round != null)
                if (Player.Count > current_round.max_players)
                    current_round.max_players = Player.Count;
        }

        void OnPlayerLeft(PlayerLeftEventArgs ev)
        {
            int id = ev.Player.PlayerId;
            if (player_sessions.ContainsKey(id))
            {
                player_sessions[ev.Player.PlayerId].disconnect = System.DateTime.Now;
                Database.Singleton.SaveTrackingSession(ev.Player);
                if (!DmRound.game_ended && !ev.Player.DoNotTrack)
                    Database.Singleton.UpdateLeaderBoard(ev.Player);
                player_sessions.Remove(id);
            }

            if (player_life.ContainsKey(id))
                player_life.Remove(id);
        }

        void OnPlayerDeath(PlayerDyingEventArgs ev)
        {
            if(ev.Player != null && ev.Attacker != null && player_life.ContainsKey(ev.Player.PlayerId) && player_life.ContainsKey(ev.Attacker.PlayerId))
            {
                Database.Life victim_life = player_life[ev.Player.PlayerId];
                Database.Life killer_life = player_life[ev.Attacker.PlayerId];
                Database.Kill kill = new Database.Kill();
                victim_life.death = kill;
                killer_life.kills.Add(kill);
                if(ev.DamageHandler is StandardDamageHandler standard)
                    kill.hitbox = standard.Hitbox;
                kill.weapon = GetItemFromDamageHandler(ev.DamageHandler);
                if (AttachmentsServerHandler.PlayerPreferences[ev.Attacker.ReferenceHub].ContainsKey(kill.weapon))
                    kill.attachment_code = AttachmentsServerHandler.PlayerPreferences[ev.Attacker.ReferenceHub][kill.weapon];
            }
        }
        
        void OnPlayerDamage(PlayerHurtingEventArgs ev)
        {
            if (config.TrackHits && ev.Player != null && ev.Attacker != null && player_life.ContainsKey(ev.Player.PlayerId) && player_life.ContainsKey(ev.Attacker.PlayerId))
            {
                if (ev.DamageHandler is StandardDamageHandler standard)
                {
                    Database.Life victim_life = player_life[ev.Player.PlayerId];
                    Database.Life attacker_life = player_life[ev.Attacker.PlayerId];
                    Database.Hit hit = new Database.Hit();
                    victim_life.received.Add(hit);
                    attacker_life.delt.Add(hit);
                    hit.health = (byte)ev.Player.Health;
                    hit.hitbox = (byte)standard.Hitbox;
                    hit.weapon = (byte)GetItemFromDamageHandler(ev.DamageHandler);
                }
            }
        }

        void OnPlayerShotWeapon(PlayerShotWeaponEventArgs ev)
        {
            if(ev.Player != null)
            {
                if (player_life.ContainsKey(ev.Player.PlayerId))
                {
                    Database.Life life = player_life[ev.Player.PlayerId];
                    if (life != null)
                        life.shots++;
                }
            }
        }

        public void UpdateLeaderBoard()
        {
            foreach(var id in player_sessions.Keys.ToList())
            {
                try
                {
                    Player player;
                    if (Player.TryGet(id, out player))
                        Database.Singleton.UpdateLeaderBoard(player);
                }
                catch(Exception ex)
                {
                    Log.Error(ex.ToString());
                }
            }
        }

        public void PlayerSpawn(Player player)
        {
            if(player != null && player_sessions.ContainsKey(player.PlayerId))
            {
                Database.Session session = player_sessions[player.PlayerId];
                Database.Life life = new Database.Life();
                Database.Loadout loadout = null;
                session.lives.Add(life);
                if (player_life.ContainsKey(player.PlayerId))
                {
                    loadout = player_life[player.PlayerId].loadout;
                    player_life[player.PlayerId] = life;
                }
                else
                    player_life.Add(player.PlayerId, life);
                life.role = Lobby.Singleton.GetSpawn(player).role;
                if (config.TrackLoadouts)
                {
                    if (loadout == null)
                        loadout = new Database.Loadout();

                    var weapon_attachments = AttachmentsServerHandler.PlayerPreferences[player.ReferenceHub];
                    Loadouts.Loadout player_loadout = Loadouts.GetLoadout(player);
                    Database.Loadout new_loadout = new Database.Loadout();

                    new_loadout.killstreak_mode = Killstreaks.GetKillstreak(player).name;
                    new_loadout.primary = player_loadout.primary;
                    if (weapon_attachments.ContainsKey(player_loadout.primary))
                        new_loadout.primary_attachment_code = weapon_attachments[player_loadout.primary];
                    new_loadout.secondary = player_loadout.secondary;
                    if (weapon_attachments.ContainsKey(player_loadout.secondary))
                        new_loadout.secondary_attachment_code = weapon_attachments[player_loadout.secondary];
                    new_loadout.tertiary = player_loadout.tertiary;
                    if (weapon_attachments.ContainsKey(player_loadout.tertiary))
                        new_loadout.tertiary_attachment_code = weapon_attachments[player_loadout.tertiary];

                    if (loadout == null || !new_loadout.Equals(loadout))
                        life.loadout = new_loadout;
                    else
                        life.loadout = loadout;
                }
            }
        }

        public Database.Session GetSession(Player player)
        {
            return player_sessions[player.PlayerId];
        }


        [CommandHandler(typeof(RemoteAdminCommandHandler))]
        public class DmDeleteUser : ICommand
        {
            public bool SanitizeResponse => false;

            public string Command { get; } = "dm_delete_user";

            public string[] Aliases { get; } = new string[] {};

            public string Description { get; } = "delete a player from the database using the players id e.g. 762394880234@steam. usage: dm_delete_user <user_id>";

            public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
            {
                if (sender is PlayerCommandSender sender1 && !sender1.CheckPermission(Singleton.config.TrackingCmdPermissions.ToArray(), out response))
                    return false;

                if (arguments.Count == 0)
                {
                    response = "usage: dm_delete_user <user_id>";
                    return false;
                }

                string user_id = arguments.At(0);
                Database.Singleton.DeleteData(user_id);

                response = "Deleting data for " + user_id + " if it exists";
                return false;
            }
        }

    }
}
