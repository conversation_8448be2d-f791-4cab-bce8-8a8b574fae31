using System;
using System.Collections.Generic;

using MEC;
using LabApi;
using LabApi.Features;
using PlayerRoles;
using UnityEngine;
using System.ComponentModel;
using CustomPlayerEffects;
using InventorySystem.Items.Firearms.Attachments;
using LightContainmentZoneDecontamination;
using PlayerStatsSystem;
using static TheRiptide.Translation;
using static RoundSummary;
using LabApi.Events;
using LabApi.Loader.Features.Plugins;
using LabApi.Features.Wrappers;
using LabApi.Events.Arguments.ServerEvents;
using Log = LabApi.Features.Console.Logger;
//todo voice and spectate cmd
namespace TheRiptide
{
    public class MainConfig
    {
        public bool IsEnabled { get; set; } = true;

        [Description("round time in minutes")]
        public float RoundTime { get; set; } = 20.0f;
        [Description("round end in seconds")]
        public float RoundEndTime { get; set; } = 30.0f;

        public string DummyPlayerName { get; set; } = "[THE RIPTIDE]";

        [Description("Enable automatic server configuration modifications (FriendlyFire, etc.). Disable this if experiencing Remote Admin config issues.")]
        public bool EnableServerConfigModifications { get; set; } = false;

        public GlobalReferenceConfig global_reference_config { get; set; } = new();
    }

    public class GlobalReferenceConfig
    {
        [Description("[AUTO GENERATED FILE] may contain types which no longer work. A reference list of types to be used in other configs (do not edit)")]
        public List<ItemType> AllItems { get; set; } = new List<ItemType>();
        public List<string> AllEffects { get; set; } = new List<string>();
        public List<AttachmentName> AllAttachments { get; set; } = new List<AttachmentName>();
        public RoomsConfig rooms_config { get; set; } = new();
        public KillstreakConfig killstreak_config { get; set; } = new();
        public LoadoutConfig loadout_config { get; set; } = new();
        public LobbyConfig lobby_config { get; set; } = new();
        public ExperienceConfig experience_config { get; set; } = new();
        public RankConfig rank_config { get; set; } = new();
        public TrackingConfig tracking_config { get; set; } = new();
        public TranslationConfig translation_config { get; set; } = new();
        public AttachmentBlacklistConfig attachment_blacklist_config { get; set; } = new();
        public VoiceChatConfig voice_chat_config { get; set; } = new();
        public CleanupConfig cleanup_config { get; set; } = new();
        public LeaderBoardConfig leader_board_config { get; set; } = new();
    }

    public class Deathmatch : Plugin<MainConfig>
    {
        public static Deathmatch Singleton { get; private set; }

        public override string Name => "火山和死人定制";

        public override string Description => "litte game!";

        public override string Author => "91VIP";

        public override Version Version => new Version(9, 1, 7, 8);

        public override Version RequiredApiVersion => new Version(1, 0, 2);


        public Deathmatch()
        {
            Singleton = this;
        }
        private List<IFeature> Features = new();
        public void Start()
        {
            try
            {
                Features = new List<IFeature>();
                Database.Singleton.Load(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"/SCP Secret Laboratory/LabAPI/configs/" + Server.Port + "/" + Name + "/" + "config.yml");

            Features.Add(new DmRound());
            //dependencies
            Features.Add(new InventoryMenu());
            Features.Add(new BroadcastOverride());
            Features.Add(new FacilityManager());
            Features.Add(new BadgeOverride());
            Features.Add(new HintOverride());
            BadgeOverride.Singleton.Init(2);

            //features
            Features.Add(new StaticEventManager());
            Features.Add(new Killstreaks());
            Features.Add(new Loadouts());
            Features.Add(new Lobby());
            Features.Add(new Rooms());
            foreach (var feature in Features)
            {
                if (feature == null)
                {
                    Log.Error("[LAB API] Feature is null, please check config.yml");
                    return;
                }
            }
            if (Config.global_reference_config.rank_config.IsEnabled)
                Features.Add(new Ranks());
            if (Config.global_reference_config.experience_config.IsEnabled)
                Features.Add(new Experiences());
            if (Config.global_reference_config.tracking_config.IsEnabled)
                Features.Add(new Tracking());
            if (Config.global_reference_config.attachment_blacklist_config.IsEnabled)
                Features.Add(new AttachmentBlacklist());
            if (Config.global_reference_config.voice_chat_config.IsEnabled)
                Features.Add(new VoiceChat());
            if (Config.global_reference_config.cleanup_config.IsEnabled)
                Features.Add(new Cleanup());
            if (Config.global_reference_config.leader_board_config.IsEnabled)
                new LeaderBoard();

            DmRound.Singleton.Init(Config);
            if (DmRound.Singleton == null)
                Log.Error("[LAB API] DmRound.Singleton is null, please check config.yml");
            Statistics.Init();
            Rooms.Singleton.Init(Config.global_reference_config.rooms_config);
            Killstreaks.Singleton.Init(Config.global_reference_config.killstreak_config);
            Loadouts.Singleton.Init(Config.global_reference_config.loadout_config);
            Lobby.Singleton.Init(Config.global_reference_config.lobby_config);
            if (Config.global_reference_config.rank_config.IsEnabled)
                Ranks.Singleton.Init(Config.global_reference_config.rank_config);
            if (Config.global_reference_config.experience_config.IsEnabled)
                Experiences.Singleton.Init(Config.global_reference_config.experience_config);
            if (Config.global_reference_config.tracking_config.IsEnabled)
                Tracking.Singleton.Init(Config.global_reference_config.tracking_config);
            if (Config.global_reference_config.attachment_blacklist_config.IsEnabled)
                AttachmentBlacklist.Singleton.Init(Config.global_reference_config.attachment_blacklist_config);
            if (Config.global_reference_config.voice_chat_config.IsEnabled)
                VoiceChat.Singleton.Init(Config.global_reference_config.voice_chat_config);
            if (Config.global_reference_config.cleanup_config.IsEnabled)
                Cleanup.Singleton.Init(Config.global_reference_config.cleanup_config);
            if (Config.global_reference_config.leader_board_config.IsEnabled)
                LeaderBoard.Singleton.Init(Config.global_reference_config.leader_board_config);

                translation = Config.global_reference_config.translation_config;
                DeathmatchMenu.Singleton.SetupMenus();

                GameCore.ConfigFile.OnConfigReloaded += DmRound.Singleton.OnConfigReloaded;
            }
            catch (Exception ex)
            {
                Log.Error("[LAB API] Plugin initialization failed: " + ex.ToString());
                throw; // 重新抛出异常以确保插件加载失败被正确处理
            }
        }

        public void Stop()
        {
            Database.Singleton.UnLoad();
            //features
            Cleanup.Singleton.Stop();
            VoiceChat.Singleton.Stop();
            AttachmentBlacklist.Singleton.Stop();
            Tracking.Singleton.Stop();
            Experiences.Singleton.Stop();
            Ranks.Singleton.Stop();
            Rooms.Singleton.Stop();
            Lobby.Singleton.Stop();
            Loadouts.Singleton.Stop();
            Killstreaks.Singleton.Stop();
            StaticEventManager.Singleton.Stop();
            DeathmatchMenu.Singleton.ClearMenus();

            GameCore.ConfigFile.OnConfigReloaded -= DmRound.Singleton.OnConfigReloaded;
        }

        public override void Enable()
        {
            if (Config.IsEnabled)
            {
                LabApi.Events.Handlers.ServerEvents.WaitingForPlayers += OnWaitingForPlayers;
                LabApi.Events.Handlers.ServerEvents.RoundEnded += OnRoundEnd;
                LabApi.Events.Handlers.ServerEvents.RoundRestarted += OnRoundRestart;
            }
            Start();
            LabApi.Events.Handlers.ServerEvents.MapGenerated += OnMapGenerated;
        }

        public override void Disable()
        {
            Stop();
        }

        public void OnMapGenerated(MapGeneratedEventArgs ev)
        {
            FacilityManager.MapGenerated(ev);
            Lobby.Singleton.MapGenerated(ev);
            if (Config.global_reference_config.rank_config.IsEnabled)
                Ranks.Singleton.MapGenerated(ev);
            if (Config.global_reference_config.leader_board_config.IsEnabled)
                LeaderBoard.Singleton.MapGenerated();
        }
        
        public void OnWaitingForPlayers()
        {
            GenerateGlobalReferenceConfig();
            DmRound.Singleton.WaitingForPlayers();
            if (Config.global_reference_config.tracking_config.IsEnabled)
                Tracking.Singleton.WaitingForPlayers();
            if (Config.global_reference_config.voice_chat_config.IsEnabled)
                VoiceChat.Singleton.WaitingForPlayers();
        }

        public void OnRoundEnd(RoundEndedEventArgs ev)
        {
            DmRound.Singleton.RoundEnd(ev);
        }

        public void OnRoundRestart()
        {
            DmRound.Singleton.RoundRestart();
            FacilityManager.RoundRestart();
            Rooms.Singleton.RoundRestart();
            if (Config.global_reference_config.cleanup_config.IsEnabled)
                Cleanup.Singleton.RoundRestart();
        }

        public static bool IsPlayerValid(Player player)
        {
            return DmRound.players.Contains(player.PlayerId);
        }



        private void GenerateGlobalReferenceConfig()
        {
            try
            {
                if (Config?.global_reference_config == null)
                {
                    Log.Error("[LAB API] Config or global_reference_config is null");
                    return;
                }

                Config.global_reference_config.AllItems.Clear();
                foreach (ItemType item in Enum.GetValues(typeof(ItemType)))
                    Config.global_reference_config.AllItems.Add(item);

                Config.global_reference_config.AllEffects.Clear();
                if (Server.Host?.GameObject != null)
                {
                    foreach (StatusEffectBase effect in Server.Host.GameObject.GetComponentsInChildren<StatusEffectBase>(true))
                        if (effect != null && !string.IsNullOrEmpty(effect.name))
                            Config.global_reference_config.AllEffects.Add(effect.name);
                }

                Config.global_reference_config.AllAttachments.Clear();
                foreach (AttachmentName name in Enum.GetValues(typeof(AttachmentName)))
                    Config.global_reference_config.AllAttachments.Add(name);
            }
            catch(Exception e)
            {
                Log.Error("[LAB API] Global reference config error - this may indicate a compatibility issue. Error: " + e.ToString());
            }
        }
    }
}
