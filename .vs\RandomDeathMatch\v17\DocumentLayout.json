{"Version": 1, "WorkspaceRootPath": "J:\\vsrepos\\Deathmatch\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{D9CEB1A1-61A3-48EA-B85C-96F78DE67169}|Deathmatch.csproj|j:\\vsrepos\\deathmatch\\features\\round.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D9CEB1A1-61A3-48EA-B85C-96F78DE67169}|Deathmatch.csproj|solutionrelative:features\\round.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Round.cs", "DocumentMoniker": "J:\\vsrepos\\Deathmatch\\Features\\Round.cs", "RelativeDocumentMoniker": "Features\\Round.cs", "ToolTip": "J:\\vsrepos\\Deathmatch\\Features\\Round.cs", "RelativeToolTip": "Features\\Round.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T18:16:35.678Z", "EditorCaption": ""}]}]}]}