﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <LangVersion>preview</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D9CEB1A1-61A3-48EA-B85C-96F78DE67169}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TheRiptide</RootNamespace>
    <AssemblyName>Deathmatch</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>embedded</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="0Harmony">
      <HintPath>D:\SCPSL_Plugins\Exiled\PluginDRY\Plugins\bin\Release\0Harmony.dll</HintPath>
    </Reference>
    <Reference Include="Assembly-CSharp-firstpass, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>F:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\Assembly-CSharp-firstpass.dll</HintPath>
    </Reference>
    <Reference Include="Assembly-CSharp_publicized, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Assembly-CSharp_publicized.dll</HintPath>
    </Reference>
    <Reference Include="AssemblyPublicizer">
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\AssemblyPublicizer.exe</HintPath>
    </Reference>
    <Reference Include="CommandSystem.Core, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\CommandSystem.Core.dll</HintPath>
    </Reference>
    <Reference Include="Glicko2">
      <HintPath>..\..\Glicko2.dll</HintPath>
    </Reference>
    <Reference Include="LabApi">
      <HintPath>D:\SCPSL_Plugins\Exiled\PluginDRY\Plugins\bin\Release\LabApi.dll</HintPath>
    </Reference>
    <Reference Include="LiteDB, Version=5.0.21.0, Culture=neutral, PublicKeyToken=4ee40123013c9f27, processorArchitecture=MSIL">
      <HintPath>packages\LiteDB.5.0.21\lib\net45\LiteDB.dll</HintPath>
    </Reference>
    <Reference Include="Mirror">
      <HintPath>F:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\Mirror.dll</HintPath>
    </Reference>
    <Reference Include="Mirror.Components">
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\Mirror.Components.dll</HintPath>
    </Reference>
    <Reference Include="Mirror_publicized">
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\publicized_assemblies\Mirror_publicized.dll</HintPath>
    </Reference>
    <Reference Include="NorthwoodLib, Version=1.2.1.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\NorthwoodLib.dll</HintPath>
    </Reference>
    <Reference Include="Pooling">
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\Pooling.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="Unity.Mathematics">
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\Unity.Mathematics.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine">
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Program Files (x86)\Steam\steamapps\common\SCP Secret Laboratory Dedicated Server\SCPSL_Data\Managed\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="YamlDotNet, Version=11.0.0.0, Culture=neutral, PublicKeyToken=ec19458f3c15af5e, processorArchitecture=MSIL">
      <HintPath>..\packages\YamlDotNet.11.0.1\lib\net45\YamlDotNet.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Dependencies\BadgeOverride.cs" />
    <Compile Include="Dependencies\BroadcastOverride.cs" />
    <Compile Include="Dependencies\FacilityManager.cs" />
    <Compile Include="Dependencies\HintOverride.cs" />
    <Compile Include="Dependencies\Teleport.cs" />
    <Compile Include="Features\AttachmentBlacklist.cs" />
    <Compile Include="Features\Cleanup.cs" />
    <Compile Include="Features\Database.cs" />
    <Compile Include="Features\Experience.cs" />
    <Compile Include="Features\IFeatures.cs" />
    <Compile Include="Features\LeaderBoard.cs" />
    <Compile Include="Features\Menus.cs" />
    <Compile Include="Dependencies\InventoryMenu.cs" />
    <Compile Include="Features\Killfeed.cs" />
    <Compile Include="Features\Killstreak.cs" />
    <Compile Include="Features\Loadout.cs" />
    <Compile Include="Deathmatch.cs" />
    <Compile Include="Features\Rank.cs" />
    <Compile Include="Features\Rooms.cs" />
    <Compile Include="Features\Round.cs" />
    <Compile Include="Features\Tracking.cs" />
    <Compile Include="Features\TranslationConfig.cs" />
    <Compile Include="Features\VoiceChat.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Features\Lobby.cs" />
    <Compile Include="Features\Stats.cs" />
    <Compile Include="Dependencies\Utility.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>