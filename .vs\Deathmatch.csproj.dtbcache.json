{"RootPath": "J:\\vsrepos\\Deathmatch", "ProjectFileName": "Deathmatch.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Dependencies\\BadgeOverride.cs"}, {"SourceFile": "Dependencies\\BroadcastOverride.cs"}, {"SourceFile": "Dependencies\\FacilityManager.cs"}, {"SourceFile": "Dependencies\\HintOverride.cs"}, {"SourceFile": "Dependencies\\Teleport.cs"}, {"SourceFile": "Features\\AttachmentBlacklist.cs"}, {"SourceFile": "Features\\Cleanup.cs"}, {"SourceFile": "Features\\Database.cs"}, {"SourceFile": "Features\\Experience.cs"}, {"SourceFile": "Features\\IFeatures.cs"}, {"SourceFile": "Features\\LeaderBoard.cs"}, {"SourceFile": "Features\\Menus.cs"}, {"SourceFile": "Dependencies\\InventoryMenu.cs"}, {"SourceFile": "Features\\Killfeed.cs"}, {"SourceFile": "Features\\Killstreak.cs"}, {"SourceFile": "Features\\Loadout.cs"}, {"SourceFile": "Deathmatch.cs"}, {"SourceFile": "Features\\Rank.cs"}, {"SourceFile": "Features\\Rooms.cs"}, {"SourceFile": "Features\\Round.cs"}, {"SourceFile": "Features\\Tracking.cs"}, {"SourceFile": "Features\\TranslationConfig.cs"}, {"SourceFile": "Features\\VoiceChat.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Features\\Lobby.cs"}, {"SourceFile": "Features\\Stats.cs"}, {"SourceFile": "Dependencies\\Utility.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\0Harmony.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\Assembly-CSharp-firstpass.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\Assembly-CSharp_publicized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\CommandSystem.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\Glicko2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\LabApi.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\Mirror.Components.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\Mirror_publicized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\NorthwoodLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\Pooling.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\Unity.Mathematics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\UnityEngine.CoreModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\UnityEngine.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\UnityEngine.PhysicsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\YamlDotNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "J:\\vsrepos\\Deathmatch\\bin\\Debug\\Deathmatch.dll", "OutputItemRelativePath": "Deathmatch.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}