using MapGeneration;
using Interactables.Interobjects.DoorUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using MEC;
using UnityEngine;
using CustomPlayerEffects;
using PlayerStatsSystem;
using System.ComponentModel;
using static TheRiptide.Translation;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Features.Wrappers;
using Log = LabApi.Features.Console.Logger;

namespace TheRiptide
{
    public class RoomsConfig
    {
        public float RoomsPerPlayer { get; set; } = 2.5f;
        [Description("how resistant surface is to changes in its open/close state. higher numbers will keep surface open/closed for longer")]
        public int SurfaceWeight { get; set; } = 5;
        [Description("decon delay. broadcast msg is not sent until time left is less than DecontaminationCaution. we want to buffer this timer to prevent frequent changes in room decon state being seen by the player i.e. lights flickering from yellow to normal")]
        public float DecontaminationTime { get; set; } = 25.0f;
        [Description("broadcast with low priority a caution message to the player. see BroadcastOverride.cs for details about priority")]
        public float DecontaminationCaution { get; set; } = 20.0f;
        [Description("broadcast with medium priority a warning message to the player")]
        public float DecontaminationWarning { get; set; } = 14.0f;
        [Description("broadcast with high priority a danger message to the player")]
        public float DecontaminationDanger { get; set; } = 7.0f;
        public float SurfaceDecontaminationTimeMultiplier { get; set; } = 2.0f;
    }

    public class Rooms : IFeature
    {
        public static Rooms Singleton { get; private set; }

        public RoomsConfig config;

        private static Dictionary<Room, int> opened_rooms = new Dictionary<Room, int>();
        private static Dictionary<Room, float> closing_rooms = new Dictionary<Room, float>();
        private static Dictionary<Room, int> closed_rooms = new Dictionary<Room, int>();

        private static CoroutineHandle update_handle = new CoroutineHandle();
        private static CoroutineHandle light_update_handle = new CoroutineHandle();
        private static CoroutineHandle decontamination_update_handle = new CoroutineHandle();

        public static IEnumerable<Room> OpenedRooms { get { return opened_rooms.Keys; } }

        public Rooms()
        {
            Singleton = this;
            LabApi.Events.Handlers.PlayerEvents.Joined += OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left += OnPlayerLeft;
            LabApi.Events.Handlers.PlayerEvents.Dying += OnPlayerDeath;
            LabApi.Events.Handlers.ServerEvents.RoundStarted += OnRoundStart;
        }

        public void Stop()
        {
            LabApi.Events.Handlers.PlayerEvents.Joined -= OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left -= OnPlayerLeft;
            LabApi.Events.Handlers.PlayerEvents.Dying -= OnPlayerDeath;
            LabApi.Events.Handlers.ServerEvents.RoundStarted -= OnRoundStart;
        }

        public void Init(RoomsConfig config)
        {
            this.config = config;
        }

        void OnRoundStart()
        {
            Log.Debug("[LAB API] Rooms OnRoundStart: Starting room management coroutines");
            update_handle = Timing.RunCoroutine(_Update());
            light_update_handle = Timing.RunCoroutine(_UpdateDecontaminationLights());
            decontamination_update_handle = Timing.RunCoroutine(_UpdateDecontaminator());
            Log.Debug("[LAB API] Rooms OnRoundStart: All coroutines started successfully");
        }

        void OnPlayerJoined(PlayerJoinedEventArgs ev)
        {
            if (Player.Count == 1)
            {
                //UnlockFacility();
                Timing.CallDelayed(7.0f, () =>
                {
                    if (Player.Count == 1)
                    {
                        UnlockFacility();
                        //open_facility = true;
                    }
                });
            }
            else if (Player.Count == 2)
            {
                LockdownFacility();
                //if (open_facility)
                //{
                //    LockdownFacility();
                //    open_facility = false;
                //}
                foreach (Player p in Player.List)
                {
                    if (p != ev.Player)
                    {
                        if (SearchForStartRoom(p))
                        {
                            BroadcastOverride.BroadcastLine(p, 1, 300.0f, BroadcastPriority.Low, translation.SecondPlayerJoined.Replace("{name}", ev.Player.Nickname));
                            BroadcastOverride.UpdateIfDirty(p);
                            Timing.CallDelayed(10.0f, () =>
                            {
                                if (ValidPlayerInRoom(p) && Deathmatch.IsPlayerValid(ev.Player) && !DmRound.GameStarted)
                                    BroadcastOverride.BroadcastLine(p, 2, 290.0f, BroadcastPriority.Low, translation.SecondPlayerHelp.Replace("{name}", ev.Player.Nickname));
                                BroadcastOverride.UpdateIfDirty(p);
                            });
                        }
                        else
                        {
                            OpenRoom(ValidRooms.ElementAt(new System.Random().Next(ValidRooms.Count())));
                        }
                        break;
                    }
                }
                ResizeFacility((int)Math.Round(Player.Count * config.RoomsPerPlayer));
            }
            else
            {
                ResizeFacility((int)Math.Round(Player.Count * config.RoomsPerPlayer));
            }
        }

        void OnPlayerLeft(PlayerLeftEventArgs ev)
        {
            if (Player.Count == 2)
            {
                UnlockFacility();
            }
            else if (Player.Count > 2)
            {
                ResizeFacility((int)Math.Round(Player.Count * config.RoomsPerPlayer));
            }
            ServerConsole.AddLog("player: " + ev.Player.Nickname + " left. player count: " + Player.Count);
        }

        void OnPlayerDeath(PlayerDyingEventArgs ev)
        {
            Log.Debug($"[LAB API] OnPlayerDeath: {ev.Player?.Nickname}, Player.Count: {Player.Count}");

            if (opened_rooms.Count == 0 && closed_rooms.Count == 0 && closing_rooms.Count == 0)
            {
                Log.Warn("[LAB API] All room dictionaries are empty! Force initializing room system...");
                if (Player.Count == 1)
                {
                    UnlockFacility();
                }
                else
                {
                    LockdownFacility();
                    ResizeFacility((int)Math.Round(Player.Count * config.RoomsPerPlayer));
                }
            }

            if (Player.Count != 1 && ev.Player != null && Deathmatch.IsPlayerValid(ev.Player))
            {
                Log.Debug("[LAB API] Player death triggered facility resize");
                ExpandFacility(1);
                ShrinkFacility(1);
            }
        }

        public void RoundRestart()
        {
            Timing.KillCoroutines(update_handle, light_update_handle, decontamination_update_handle);
        }

        private static IEnumerable<Room> ValidRooms => Room.List.Where((r) => r.Zone != FacilityZone.Other && r.Zone != FacilityZone.None);

        public void UnlockFacility()
        {
            Log.Debug($"[LAB API] UnlockFacility called - ValidRooms count: {ValidRooms.Count()}");
            FacilityManager.UnlockAllRooms(DoorLockReason.AdminCommand);
            FacilityManager.OpenAllRooms();
            FacilityManager.ResetAllRoomLights();
            opened_rooms.Clear();
            foreach (var room in ValidRooms)
            {
                opened_rooms.Add(room, RoomWeight(room));
                Log.Debug($"[LAB API] Added room to opened_rooms: {room.Name} in zone {room.Zone}");
            }
            closing_rooms.Clear();
            closed_rooms.Clear();
            Log.Debug($"[LAB API] UnlockFacility completed - opened_rooms count: {opened_rooms.Count}");
        }

        public void LockdownFacility()
        {
            Log.Debug($"[LAB API] LockdownFacility called - ValidRooms count: {ValidRooms.Count()}");
            FacilityManager.LockAllRooms(DoorLockReason.AdminCommand);
            FacilityManager.CloseAllRooms();
            FacilityManager.SetAllRoomLightColors(new Color(1.0f, 0.0f, 0.0f));
            opened_rooms.Clear();
            closing_rooms.Clear();
            closed_rooms.Clear();
            foreach (var room in ValidRooms)
                closed_rooms.Add(room, RoomWeight(room));
            Log.Debug($"[LAB API] LockdownFacility completed - closed_rooms count: {closed_rooms.Count}");
        }

        private bool SearchForStartRoom(Player player)
        {
            Log.Debug($"[LAB API] SearchForStartRoom: 为玩家 {player.Nickname} 搜索起始房间");

            Room startRoom = GetPlayerStartRoom(player);
            if (startRoom != null)
            {
                Log.Info($"[LAB API] SearchForStartRoom: 以玩家 {player.Nickname} 的位置为中心，选择房间 {startRoom.Name} ({startRoom.Zone}) 作为游戏区域起点");
                OpenRoom(startRoom);
                return true;
            }
            else
            {
                Log.Warn($"[LAB API] SearchForStartRoom: 无法为玩家 {player.Nickname} 找到合适的起始房间");
                return false;
            }
        }

        private void ResizeFacility(int count)
        {
            Log.Debug($"[LAB API] ResizeFacility called - target: {count}, current opened: {opened_rooms.Count()}, closing: {closing_rooms.Count()}, closed: {closed_rooms.Count()}");
            if (count < opened_rooms.Count())
            {
                int shrinkCount = opened_rooms.Count() - count;
                Log.Debug($"[LAB API] Shrinking facility by {shrinkCount} rooms");
                ShrinkFacility(shrinkCount);
            }
            else if (count > opened_rooms.Count())
            {
                int expandCount = count - opened_rooms.Count();
                Log.Debug($"[LAB API] Expanding facility by {expandCount} rooms");
                ExpandFacility(expandCount);
            }
            else
            {
                Log.Debug("[LAB API] Facility size already correct, no changes needed");
            }
        }

        public void ExpandFacility(int count)
        {
            Log.Debug($"[LAB API] ExpandFacility called with count: {count}");
            if (opened_rooms.IsEmpty())
            {
                Log.Debug("[LAB API] No opened rooms, searching for start rooms");
                foreach (var player in Player.List)
                    if (SearchForStartRoom(player))
                    {
                        Log.Debug($"[LAB API] Found start room for player {player.Nickname}");
                        break;
                    }
                if (opened_rooms.IsEmpty())
                {
                    Log.Debug($"[LAB API] Still no opened rooms, opening random room from {ValidRooms.Count()} valid rooms");
                    if (ValidRooms.Count() > 0)
                    {
                        var randomRoom = ValidRooms.ElementAt(new System.Random().Next(ValidRooms.Count()));
                        Log.Debug($"[LAB API] Opening random room: {randomRoom.Name}");
                        OpenRoom(randomRoom);
                    }
                    else
                    {
                        Log.Error("[LAB API] No valid rooms available for expansion!");
                    }
                }
                count--;
            }

            for (int i = 0; i < count; i++)
            {
                HashSet<Room> candidate_set = new HashSet<Room>();
                HashSet<Room> backup_set = new HashSet<Room>();
                foreach (var room in opened_rooms.Keys)
                {
                    foreach (var adj in FacilityManager.GetAdjacent(room.Base).Keys)
                    {
                        var adj1 = Room.List.Where(r => r.Base == adj).FirstOrDefault();
                        if (adj1 != null)
                        {
                            if (closed_rooms.ContainsKey(adj1))
                                candidate_set.Add(adj1);
                            else if (closing_rooms.ContainsKey(adj1))
                                backup_set.Add(adj1);
                        }
                    }
                }

                if (!candidate_set.IsEmpty())
                {
                    System.Random random = new System.Random();
                    int maxAttempts = candidate_set.Count * 10; // 防止无限循环
                    int attempts = 0;
                    while (attempts < maxAttempts)
                    {
                        if (candidate_set.Count == 0) break; // 安全检查
                        var room = candidate_set.ElementAt(random.Next(candidate_set.Count()));
                        closed_rooms[room]--;
                        if (closed_rooms[room] <= 0)
                        {
                            OpenRoom(room);
                            break;
                        }
                        attempts++;
                    }
                    if (attempts >= maxAttempts)
                    {
                        Log.Warn("[LAB API] Room expansion loop reached maximum attempts, forcing room open");
                        if (candidate_set.Count > 0)
                        {
                            var room = candidate_set.First();
                            closed_rooms[room] = 0;
                            OpenRoom(room);
                        }
                    }
                }
                else if (!backup_set.IsEmpty())
                {
                    Room max = backup_set.First();
                    backup_set.Remove(max);
                    foreach (var room in backup_set)
                        if (closing_rooms[room] > closing_rooms[max])
                            max = room;
                    OpenRoom(max);
                }
                else
                    break;
            }
        }

        public void ShrinkFacility(int count)
        {
            System.Random random = new System.Random();
            for (int i = 0; i < count; i++)
            {
                if (!opened_rooms.IsEmpty())
                {
                    Dictionary<Room, bool> visited = new Dictionary<Room, bool>();
                    foreach (var room in opened_rooms.Keys)
                        visited.Add(room, false);

                    Room dsf_room = null;
                    Action<Room> DFS = null;
                    DFS = (node) =>
                    {
                        visited[node] = true;
                        dsf_room = node;
                        foreach (var adj in FacilityManager.GetAdjacent(node.Base).Keys)
                        {
                            var adj1 = Room.List.Where(r => r.Base == adj).FirstOrDefault();
                            if (adj1 != null && closed_rooms.ContainsKey(adj1) && !visited[adj1])
                                DFS(adj1);
                        }
                    };

                    int maxAttempts = opened_rooms.Count * 10; // 防止无限循环
                    int attempts = 0;
                    while (attempts < maxAttempts)
                    {
                        if (opened_rooms.Count == 0) break; // 安全检查
                        DFS(opened_rooms.ElementAt(random.Next(opened_rooms.Count())).Key);
                        foreach (var room in opened_rooms.Keys)
                            visited[room] = false;

                        // 检查 dsf_room 是否在 closing_rooms 中
                        if (closing_rooms.ContainsKey(dsf_room))
                        {
                            opened_rooms[dsf_room]--;
                            if (opened_rooms[dsf_room] <= 0)
                            {
                                ClosingRoom(dsf_room);
                                break;
                            }
                        }
                        else
                        {
                            Log.Warn($"[LAB API] Room {dsf_room.Name} in zone {dsf_room.Zone} not found in closing_rooms dictionary");
                            break;
                        }
                        attempts++;
                    }

                    if (attempts >= maxAttempts)
                    {
                        Log.Warn("[LAB API] Room shrinking loop reached maximum attempts, forcing room close");
                        if (opened_rooms.Count > 0)
                        {
                            var room = opened_rooms.Keys.First();
                            opened_rooms[room] = 0;
                            CloseRoom(room);
                        }
                    }
                }
                else
                {
                    break;
                }
            }
        }


        public static bool ValidPlayerInRoom(Player player)
        {
            return player.IsAlive && player.Room != null && Deathmatch.IsPlayerValid(player) && !Lobby.Singleton.InSpawn(player);
        }

        private static IEnumerator<float> _UpdateDecontaminator()
        {
            const float delta = 1.0f;
            while (true)
            {
                try
                {
                    foreach (Player player in Player.List)
                    {
                        if (ValidPlayerInRoom(player) && closed_rooms.Keys.Contains(player.Room))
                        {
                            BroadcastOverride.BroadcastLine(player, 1, delta, BroadcastPriority.High, translation.Decontaminating);
                            BroadcastOverride.UpdateIfDirty(player);
                            player.EnableEffect<Decontaminating>(1);
                        }
                    }
                }
                catch (Exception ex)
                {
                    ServerConsole.AddLog("Rooms._UpdateDecontaminator() error: " + ex.ToString(), ConsoleColor.White);
                }
                yield return Timing.WaitForSeconds(delta);
            }
        }

        private IEnumerator<float> _UpdateDecontaminationLights()
        {
            const float delta = 0.2f;
            while (true)
            {
                try
                {
                    foreach (var pair in closing_rooms)
                    {
                        bool is_surface = IsSurface(pair.Key);
                        float caution = (is_surface ? config.DecontaminationCaution * config.SurfaceDecontaminationTimeMultiplier : config.DecontaminationCaution) + 1.0f;
                        if (caution >= pair.Value)
                        {
                            float x = pair.Value / caution;
                            FacilityManager.SetRoomLightColor(pair.Key.Base, new Color(1.0f, x, 0.0f));
                        }
                    }
                }
                catch (Exception ex)
                {
                    ServerConsole.AddLog("Rooms._UpdateDecontaminationLights() error: " + ex.ToString(), ConsoleColor.White);
                }
                yield return Timing.WaitForSeconds(delta);
            }
        }

        private IEnumerator<float> _Update()
        {
            const float delta = 1.0f / 7.0f;
            while (true)
            {
                try
                {
                    //warn players inside rooms marked for closing
                    foreach (Player player in Player.List)
                    {
                        if (ValidPlayerInRoom(player) && closing_rooms.ContainsKey(player.Room))
                        {
                            bool is_surface = IsSurface(player.Room);
                            float caution = (is_surface ? config.DecontaminationCaution * config.SurfaceDecontaminationTimeMultiplier : config.DecontaminationCaution) + 1.0f;
                            float warning = (is_surface ? config.DecontaminationWarning * config.SurfaceDecontaminationTimeMultiplier : config.DecontaminationWarning) + 1.0f;
                            float danger = (is_surface ? config.DecontaminationDanger * config.SurfaceDecontaminationTimeMultiplier : config.DecontaminationDanger) + 1.0f;
                            float time = closing_rooms[player.Room];
                            if (Math.Abs(time - Math.Round(time)) <= (delta / 2.0f) || danger >= time)
                            {
                                if (caution >= time && time > warning)
                                    BroadcastOverride.BroadcastLine(player, 1, 1.0f + delta, BroadcastPriority.Low, translation.Caution.Replace("{time}", Math.Floor(time - 1.0f).ToString("0")));
                                else if (warning >= time && time > danger)
                                    BroadcastOverride.BroadcastLine(player, 1, 1.0f + delta, BroadcastPriority.Medium, translation.Warning.Replace("{time}", Math.Floor(time - 1.0f).ToString("0")));
                                else if (danger >= time)
                                    BroadcastOverride.BroadcastLine(player, 1, delta, BroadcastPriority.High, translation.Danger.Replace("{time}", time.ToString("0.000")));
                                BroadcastOverride.UpdateIfDirty(player);
                            }
                        }
                    }

                    List<Room> close = new List<Room>();
                    foreach (Room key in closing_rooms.Keys.ToList())
                    {
                        closing_rooms[key] -= delta;
                        if (closing_rooms[key] < 0.0f)
                            close.Add(key);
                    }

                    foreach (Room room in close)
                    {
                        ClosingRoom(room);
                        CloseRoom(room); // 确保房间被关闭后也被添加到 closed_rooms 字典中
                    }
                }
                catch (Exception ex)
                {
                    ServerConsole.AddLog("Rooms._Update() Error: " + ex.Message + " in " + ex.StackTrace, ConsoleColor.White);
                }

                yield return Timing.WaitForSeconds(delta);
            }
        }

        private void OpenRoom(Room room)
        {
            FacilityManager.ResetRoomLight(room.Base);
            HashSet<Room> joined_rooms = new HashSet<Room>();
            joined_rooms.Add(room);
            foreach (var adj in FacilityManager.GetAdjacent(room.Base).Keys)
            {
                Room adj1 = Room.List.Where(r => r.Base == adj).FirstOrDefault();
                if (adj1 != null && (opened_rooms.ContainsKey(adj1) || closing_rooms.ContainsKey(adj1)))
                    joined_rooms.Add(adj1);
            }
            FacilityManager.UnlockJoinedRooms(joined_rooms.Select(r => r.Base).ToHashSet(), DoorLockReason.AdminCommand);
            if (!opened_rooms.ContainsKey(room))
                opened_rooms.Add(room, RoomWeight(room));
            closing_rooms.Remove(room);
            closed_rooms.Remove(room);
        }

        private void ClosingRoom(Room room)
        {
            if (!closing_rooms.ContainsKey(room))
                closing_rooms.Add(room, RoomDecontaminationTime(room));
            opened_rooms.Remove(room);
            closed_rooms.Remove(room);
        }

        private void CloseRoom(Room room)
        {
            if (closing_rooms.ContainsKey(room))
            {
                FacilityManager.CloseRoom(room.Base);
                FacilityManager.LockRoom(room.Base, DoorLockReason.AdminCommand);
                FacilityManager.SetRoomLightColor(room.Base, new Color(1.0f, 0.0f, 0.0f));
                if (!closed_rooms.ContainsKey(room))
                    closed_rooms.Add(room, RoomWeight(room));
                opened_rooms.Remove(room);
                closing_rooms.Remove(room);

                // 禁用净化效果
                foreach (var player in Player.List)
                {
                    if (player.Room == room)
                    {
                        player.DisableEffect<Decontaminating>();
                    }
                }
            }
            else
            {
                Log.Warn($"[LAB API] Room {room.Name} in zone {room.Zone} not found in closing_rooms dictionary");
            }
        }


        private static bool IsSurface(Room room)
        {
            return room.Zone == FacilityZone.Surface;
        }

        private float RoomDecontaminationTime(Room room)
        {
            if (room.Zone == FacilityZone.Surface)
                return config.DecontaminationTime * config.SurfaceDecontaminationTimeMultiplier;
            else
            {
                float extened_decontamination_time = config.DecontaminationTime;
                foreach (var adj in FacilityManager.GetAdjacent(room.Base).Keys)
                {
                    var adj1 = Room.List.Where(r => r.Base == adj).FirstOrDefault();
                    if (adj1 != null && closing_rooms.ContainsKey(adj1) && closing_rooms[adj1] > extened_decontamination_time)
                        extened_decontamination_time = closing_rooms[adj1];
                }
                return extened_decontamination_time;
            }
        }

        private int RoomWeight(Room room)
        {
            if (room.Zone == FacilityZone.Surface)
                return config.SurfaceWeight;
            else
                return 1;
        }
    }
}
