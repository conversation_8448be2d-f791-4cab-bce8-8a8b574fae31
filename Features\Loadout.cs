﻿using InventorySystem.Items;
using InventorySystem.Items.Firearms;
using InventorySystem.Items.Pickups;
using InventorySystem.Items.Radio;
using MapGeneration;
using Interactables.Interobjects;
using Mirror;
using MEC;
using PlayerRoles;
using PlayerStatsSystem;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using Unity.Mathematics;
using static TheRiptide.Utility;
using static TheRiptide.Translation;
using Interactables.Interobjects.DoorUtils;
using LabApi.Features.Wrappers;
using LabApi.Events.Arguments.PlayerEvents;
using System.Reflection;
using Log = LabApi.Features.Console.Logger;

namespace TheRiptide
{
    public class LoadoutConfig
    {
        public bool IsBlackListEnabled { get; set; } = true;

        [Description("put black listed weapons here, see below for all weapons. does not effect weapons granted as a reward only guns on the menu")]
        public List<ItemType> BlackList { get; set; } = new List<ItemType>();


        [Description("list of all the different weapons (changing this does nothing)")]
        public List<ItemType> AllWeapons { get; set; } = new List<ItemType>
        {
            ItemType.GunAK,
            ItemType.GunCOM15,
            ItemType.GunCOM18,
            ItemType.GunCom45,
            ItemType.GunCrossvec,
            ItemType.GunE11SR,
            ItemType.GunFSP9,
            ItemType.GunLogicer,
            ItemType.GunRevolver,
            ItemType.GunShotgun
        };
    }

    public class Loadouts : IFeature
    {
        public static Loadouts Singleton { get; private set; }

        LoadoutConfig config;

        public enum GunSlot { Primary, Secondary, Tertiary };

        public class Loadout
        {
            public ItemType primary = ItemType.None;
            public ItemType secondary = ItemType.None;
            public ItemType tertiary = ItemType.None;
            public GunSlot slot = GunSlot.Primary;

            public bool locked = false;
            public bool customising = false;
            public bool rage_mode_enabled = false;
        }

        public static Dictionary<int, Loadout> player_loadouts = new Dictionary<int, Loadout>();

        public Loadouts()
        {
            Singleton = this;
            LabApi.Events.Handlers.ServerEvents.RoundStarted += OnRoundStart;
            LabApi.Events.Handlers.PlayerEvents.Joined += OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left += OnPlayerLeft;
            LabApi.Events.Handlers.PlayerEvents.Spawning += OnPlayerSpawn;
            LabApi.Events.Handlers.PlayerEvents.DroppingItem += OnPlayerDropItem;
            LabApi.Events.Handlers.PlayerEvents.Dying += OnPlayerDying;
            LabApi.Events.Handlers.PlayerEvents.UsedItem += OnPlayerUsedItem;
            LabApi.Events.Handlers.PlayerEvents.ShotWeapon += OnPlayerShotWeapon;
            LabApi.Events.Handlers.PlayerEvents.InteractingDoor += OnPlayerInteractDoor;
            LabApi.Events.Handlers.PlayerEvents.DroppingAmmo += OnPlayerDropAmmo;
        }
        public void Stop()
        {
            LabApi.Events.Handlers.ServerEvents.RoundStarted -= OnRoundStart;
            LabApi.Events.Handlers.PlayerEvents.Joined -= OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left -= OnPlayerLeft;
            LabApi.Events.Handlers.PlayerEvents.Spawning -= OnPlayerSpawn;
            LabApi.Events.Handlers.PlayerEvents.DroppingItem -= OnPlayerDropItem;
            LabApi.Events.Handlers.PlayerEvents.Dying -= OnPlayerDying;
            LabApi.Events.Handlers.PlayerEvents.UsedItem -= OnPlayerUsedItem;
            LabApi.Events.Handlers.PlayerEvents.ShotWeapon -= OnPlayerShotWeapon;
            LabApi.Events.Handlers.PlayerEvents.InteractingDoor -= OnPlayerInteractDoor;
            LabApi.Events.Handlers.PlayerEvents.DroppingAmmo -= OnPlayerDropAmmo;
        }
        public void Init(LoadoutConfig config)
        {
            this.config = config;
        }

        void OnRoundStart()
        {
            var scp330 = Scp330Interobject.FindObjectOfType<Scp330Interobject>();
            NetworkServer.UnSpawn(scp330.gameObject);
        }

        void OnPlayerJoined(PlayerJoinedEventArgs ev)
        {
            if (!player_loadouts.ContainsKey(ev.Player.PlayerId))
                player_loadouts.Add(ev.Player.PlayerId, new Loadout());
        }

        void OnPlayerLeft(PlayerLeftEventArgs ev)
        {
            if (player_loadouts.ContainsKey(ev.Player.PlayerId))
            {
                if (!ev.Player.DoNotTrack)
                    Database.Singleton.SaveConfigLoadout(ev.Player);
                player_loadouts.Remove(ev.Player.PlayerId);
            }
        }

        void OnPlayerDropItem(PlayerDroppingItemEventArgs ev)
        {
            bool drop_allowed = false;
            if (!player_loadouts.ContainsKey(ev.Player.PlayerId))
            {
                Log.Warn($"[LAB API] Player {ev.Player.Nickname} (ID: {ev.Player.PlayerId}) not found in loadouts dictionary for OnPlayerDropItem");
                return;
            }
            Loadout loadout = player_loadouts[ev.Player.PlayerId];
            if (InventoryMenu.GetPlayerMenuID(ev.Player) == 0)
            {
                if (IsGun(ev.Item.Type))
                {
                    if (!loadout.locked)
                    {
                        if (ev.Item.Type == loadout.primary)
                        {
                            loadout.primary = ItemType.None;
                            RemoveItem(ev.Player, ev.Item.Type);
                        }
                        else if (ev.Item.Type == loadout.secondary)
                        {
                            loadout.secondary = ItemType.None;
                            RemoveItem(ev.Player, ev.Item.Type);
                        }
                        else if (ev.Item.Type == loadout.tertiary)
                        {
                            loadout.tertiary = ItemType.None;
                            RemoveItem(ev.Player, ev.Item.Type);
                        }

                        if (IsLoadoutEmpty(ev.Player))
                        {
                            BroadcastOverride.ClearLines(ev.Player, BroadcastPriority.High);
                            BroadcastOverride.BroadcastLine(ev.Player, 1, 300, BroadcastPriority.High, translation.CustomisationHint);
                            if (Lobby.Singleton.InSpawn(ev.Player))
                            {
                                Lobby.Singleton.CancelTeleport(ev.Player);
                                BroadcastOverride.BroadcastLine(ev.Player, 2, 300, BroadcastPriority.High, translation.Teleport);
                            }
                        }
                    }
                    else if (!Killstreaks.Singleton.IsLoadoutLocked(ev.Player))
                    {
                        BroadcastOverride.ClearLines(ev.Player, BroadcastPriority.High);
                        BroadcastOverride.BroadcastLines(ev.Player, 1, 3, BroadcastPriority.High, translation.CustomisationDenied);
                    }
                    else
                    {
                        int gun_count = 0;
                        foreach (var i in ev.Player.ReferenceHub.inventory.UserInventory.Items.Values)
                            if (IsGun(i.ItemTypeId))
                                gun_count++;
                        if (gun_count >= 2)
                            RemoveItem(ev.Player, ev.Item.Type);
                        else
                            BroadcastOverride.BroadcastLine(ev.Player, 1, 3, BroadcastPriority.High, translation.LastWeapon);
                    }
                }
                else if (ev.Item.Category != ItemCategory.Armor && loadout.locked)
                    drop_allowed = true;
            }
            BroadcastOverride.UpdateIfDirty(ev.Player);
            ev.IsAllowed = drop_allowed;
        }
        void OnPlayerDropAmmo(PlayerDroppingAmmoEventArgs ev)
        {
            ev.IsAllowed = false;
        }

        void OnPlayerShotWeapon(PlayerShotWeaponEventArgs ev)
        {
            if (player_loadouts.ContainsKey(ev.Player.PlayerId))
            {
                player_loadouts[ev.Player.PlayerId].locked = true;
                RemoveItem(ev.Player, ItemType.KeycardO5);
            }
        }

        void OnPlayerUsedItem(PlayerUsedItemEventArgs ev)
        {
            if (player_loadouts.ContainsKey(ev.Player.PlayerId))
            {
                player_loadouts[ev.Player.PlayerId].locked = true;
                RemoveItem(ev.Player, ItemType.KeycardO5);
            }
        }

        void OnPlayerInteractDoor(PlayerInteractingDoorEventArgs ev)
        {
            if(ev.Door.IsLocked)
                return;
            if (ev.Door.Base.ActiveLocks > 0 && !ev.Player.IsBypassEnabled)
                ev.IsAllowed = true;

            if (ev.Door.Base.AllowInteracting(ev.Player.ReferenceHub, 0))
            {
                ev.Door.IsOpened = !ev.Door.IsOpened;
                System.Reflection.FieldInfo field = typeof(DoorVariant).GetField("_triggerPlayer", BindingFlags.NonPublic | BindingFlags.Instance);
                field.SetValue(ev.Door.Base, ev.Player.ReferenceHub);
                switch (ev.Door.Base.NetworkTargetState)
                {
                    case false:
                        DoorEvents.TriggerAction(ev.Door.Base, DoorAction.Closed, ev.Player.ReferenceHub);
                        break;
                    case true:
                        DoorEvents.TriggerAction(ev.Door.Base, DoorAction.Opened, ev.Player.ReferenceHub);
                        break;
                }
            }
            ev.IsAllowed = false;
        }

        void OnPlayerDying(PlayerDyingEventArgs ev)
        {
            ev.Player.ClearInventory();
        }

        void OnPlayerSpawn(PlayerSpawningEventArgs ev)
        {
            if (ev.Player == null || !player_loadouts.ContainsKey(ev.Player.PlayerId))
                return;

            Loadout loadout = player_loadouts[ev.Player.PlayerId];
            if (config.IsBlackListEnabled)
            {
                if (config.BlackList.Contains(loadout.primary))
                    loadout.primary = ItemType.None;
                if (config.BlackList.Contains(loadout.secondary))
                    loadout.secondary = ItemType.None;
                if (config.BlackList.Contains(loadout.tertiary))
                    loadout.tertiary = ItemType.None;
            }

            if (Lobby.Singleton.GetSpawn(ev.Player).role == ev.Role.RoleTypeId)
            {
                loadout.locked = false;

                Timing.CallDelayed(0.0f, () =>
                {
                   if (ev.Player == null || !player_loadouts.ContainsKey(ev.Player.PlayerId))
                        return;
                    ev.Player.ClearInventory();
                      AddLoadoutStartItems(ev.Player);
                });
            }
        }

        public static bool ValidateLoadout(Player player)
        {
            bool isEmpty = IsLoadoutEmpty(player);
            Log.Debug($"[LAB API] ValidateLoadout for {player.Nickname}: isEmpty={isEmpty}");

            if (isEmpty)
            {
                BroadcastOverride.BroadcastLine(player, 1, 300, BroadcastPriority.High, translation.CustomisationHint);
                return false;
            }
            else
                return true;
        }

        public static Loadout GetLoadout(Player player)
        {
            if (!player_loadouts.ContainsKey(player.PlayerId))
            {
                Log.Warn($"[LAB API] Player {player.Nickname} (ID: {player.PlayerId}) not found in loadouts dictionary for GetLoadout");
                return new Loadout(); // 返回默认loadout
            }
            return player_loadouts[player.PlayerId];
        }

        public static bool CustomiseLoadout(Player player)
        {
            Loadout loadout = GetLoadout(player);
            if (!loadout.locked)
            {
                loadout.customising = true;
                // 只在玩家在等待室时取消传送，避免在游戏中误取消
                if (Lobby.Singleton.InSpawn(player))
                {
                    Log.Debug($"[LAB API] CustomiseLoadout: Cancelling teleport for {player.Nickname} (in spawn)");
                    Lobby.Singleton.CancelTeleport(player);
                }
                else
                {
                    Log.Debug($"[LAB API] CustomiseLoadout: Not cancelling teleport for {player.Nickname} (not in spawn)");
                }
                return true;
            }
            else
            {
                BroadcastOverride.ClearLines(player, BroadcastPriority.High);
                BroadcastOverride.BroadcastLines(player, 1, 3, BroadcastPriority.High, translation.CustomisationDenied);
                return false;
            }
        }

        public static bool IsLoadoutEmpty(Player player)
        {
            if (!player_loadouts.ContainsKey(player.PlayerId))
            {
                Log.Warn($"[LAB API] Player {player.Nickname} (ID: {player.PlayerId}) not found in loadouts dictionary for IsLoadoutEmpty");
                return true; // 默认为空
            }

            Loadout loadout = player_loadouts[player.PlayerId];
            bool isLocked = Killstreaks.Singleton.IsLoadoutLocked(player);
            ItemType armor = Killstreaks.Singleton.ArmorType(player);

            Log.Debug($"[LAB API] IsLoadoutEmpty for {player.Nickname}: primary={loadout.primary}, secondary={loadout.secondary}, tertiary={loadout.tertiary}, armor={armor}, isLocked={isLocked}");

            if (isLocked)
                return false;

            if (armor == ItemType.None)
                return loadout.primary == ItemType.None;
            else if (armor == ItemType.ArmorLight || armor == ItemType.ArmorCombat)
                return loadout.primary == ItemType.None && loadout.secondary == ItemType.None;
            else
                return loadout.primary == ItemType.None && loadout.secondary == ItemType.None && loadout.tertiary == ItemType.None;
        }

        public static void AddLoadoutStartItems(Player player)
        {
            if (!player_loadouts.ContainsKey(player.PlayerId))
            {
                Log.Warn($"[LAB API] Player {player.Nickname} (ID: {player.PlayerId}) not found in loadouts dictionary for AddLoadoutStartItems");
                return;
            }

            Loadout loadout = player_loadouts[player.PlayerId];
            Killstreaks.Killstreak killstreak = Killstreaks.GetKillstreak(player);

            if (!IsLoadoutEmpty(player))
            {
                ItemType armor = Killstreaks.Singleton.ArmorType(player);
                if (armor != ItemType.None)
                    AddArmor(player, armor, true);

                Killstreaks.Singleton.AddKillstreakStartAmmo(player);
                if (!Killstreaks.Singleton.IsLoadoutLocked(player))
                {
                    AddFirearm(player, loadout.primary, false);
                    if (armor != ItemType.None)
                        AddFirearm(player, loadout.secondary, false);
                    if(armor == ItemType.ArmorHeavy)
                        AddFirearm(player, loadout.tertiary, false);
                }
                Killstreaks.Singleton.AddKillstreakStartItems(player);
                if (!Lobby.Singleton.InSpawn(player) && DmRound.GameStarted)
                    Killstreaks.Singleton.AddKillstreakStartEffects(player);
            }
            player.AddItem(ItemType.KeycardO5);
        }

        public bool SetGun(Player player, ItemType gun)
        {
            if (!player_loadouts.ContainsKey(player.PlayerId))
            {
                Log.Warn($"[LAB API] Player {player.Nickname} (ID: {player.PlayerId}) not found in loadouts dictionary for SetGun");
                return false;
            }

            Loadout loadout = player_loadouts[player.PlayerId];

            if(config.IsBlackListEnabled && config.BlackList.Contains(gun))
            {
                BroadcastOverride.BroadcastLine(player, 2, 5.0f, BroadcastPriority.High, translation.WeaponBanned.Replace("{weapon}", gun.ToString().Substring(3)));
                return false;
            }
            if (loadout.slot == GunSlot.Primary)
                loadout.primary = gun;
            else if (loadout.slot == GunSlot.Secondary)
                loadout.secondary = gun;
            else if (loadout.slot == GunSlot.Tertiary)
                loadout.tertiary = gun;
            return true;
        }
    }
}
