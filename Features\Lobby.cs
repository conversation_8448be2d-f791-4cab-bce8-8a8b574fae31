using AdminToys;
using LabApi.Features.Wrappers;
using MapGeneration;
using MEC;
using Mirror;
using PlayerRoles;
using PlayerStatsSystem;
using Respawning;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using UnityEngine;
using Log = LabApi.Features.Console.Logger;
using static TheRiptide.Translation;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Events.Arguments.ServerEvents;
using PrimitiveObjectToy = AdminToys.PrimitiveObjectToy;
using LightSourceToy = AdminToys.LightSourceToy;

namespace TheRiptide
{
    // 游戏大厅配置类，包含玩家出生点颜色、光照强度、出生点尺寸和保护时间等信息
    public class LobbyConfig
    {
        public int SpawnColorRed{ get; set; } = 67;
        public int SpawnColorGreen { get; set; } = 191;
        public int SpawnColorBlue { get; set; } = 240;
        public float SpawnLightIntensity { get; set; } = 100.00f;
        [Description("max players should be less than SpawnDimX x SpawnDimY")]
        public int SpawnDimX { get; set; } = 8;
        public int SpawnDimY { get; set; } = 8;
        public float SpawnProtection { get; set; } = 3.0f;
    }

    // 游戏大厅类，实现IFeature接口，管理玩家出生点、游戏开始逻辑等
    public class Lobby : IFeature
    {
        public static Lobby Singleton { get; private set; }

        LobbyConfig config;

        // 玩家出生点信息类，包含角色类型、是否在出生点、是否在旁观者模式、传送协程句柄和出生点房间编号
        public class Spawn
        {
            public RoleTypeId role = RoleTypeId.ClassD;
            public bool in_spawn = true;
            public bool in_spectator_mode = false;
            public CoroutineHandle teleport_handle;
            public int spawn_room = -1;
        }

        // 玩家出生点字典，键为玩家ID，值为Spawn对象
        public Dictionary<int, Spawn> player_spawns = new Dictionary<int, Spawn>();
        // 可用出生点房间编号集合
        private SortedSet<int> avaliable_spawn_rooms = new SortedSet<int>();
        // 存储出生点区域的方块对象
        private List<GameObject> blocks = new List<GameObject>();
        // 游戏是否已经开始
        private bool round_started = false;

        // 构造函数，初始化单例并订阅多个事件处理程序
        public Lobby()
        {
            Singleton = this;
            LabApi.Events.Handlers.ServerEvents.WaveRespawning += OnRespawn;
            LabApi.Events.Handlers.PlayerEvents.Escaping += OnPlayerEscape;
            LabApi.Events.Handlers.ServerEvents.MapGenerated += MapGenerated;
            LabApi.Events.Handlers.PlayerEvents.Dying += OnPlayerDeath;
            LabApi.Events.Handlers.PlayerEvents.Spawning += OnPlayerSpawn;
            LabApi.Events.Handlers.PlayerEvents.Joined += OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left += OnPlayerLeft;
            LabApi.Events.Handlers.PlayerEvents.ChangingRole += OnPlayerChangeRole;
            LabApi.Events.Handlers.PlayerEvents.ChangedSpectator += OnPlayerChangeSpectator;
        }

        // 停止方法，取消订阅所有事件处理程序
        public void Stop()
        {
            LabApi.Events.Handlers.ServerEvents.WaveRespawning -= OnRespawn;
            LabApi.Events.Handlers.PlayerEvents.Escaping -= OnPlayerEscape;
            LabApi.Events.Handlers.ServerEvents.MapGenerated -= MapGenerated;
            LabApi.Events.Handlers.PlayerEvents.Dying -= OnPlayerDeath;
            LabApi.Events.Handlers.PlayerEvents.Spawning -= OnPlayerSpawn;
            LabApi.Events.Handlers.PlayerEvents.Joined -= OnPlayerJoined;
            LabApi.Events.Handlers.PlayerEvents.Left -= OnPlayerLeft;
            LabApi.Events.Handlers.PlayerEvents.ChangingRole -= OnPlayerChangeRole;
            LabApi.Events.Handlers.PlayerEvents.ChangedSpectator -= OnPlayerChangeSpectator;
        }

        // 初始化方法，配置游戏大厅并清除可用出生点房间集合
        public void Init(LobbyConfig config)
        {
            this.config = config;
            avaliable_spawn_rooms.Clear();
            for (int i = 0; i < config.SpawnDimX * config.SpawnDimY; i++)
                avaliable_spawn_rooms.Add(i);
        }

        // 地图生成事件处理程序，延迟调用BuildSpawn方法构建出生点区域
        public void MapGenerated(MapGeneratedEventArgs ev)
        {
            Timing.CallDelayed(0.0f, () =>
            {
                BuildSpawn(config.SpawnDimX, config.SpawnDimY);
            });
            round_started = false;

            //foreach (var c in NetworkManager.singleton.spawnPrefabs)
            //    Log.Info(c.name);
        }

        // 玩家加入事件处理程序，添加玩家出生点信息并启动游戏
        void OnPlayerJoined(PlayerJoinedEventArgs ev)
        {
            if (!player_spawns.ContainsKey(ev.Player.PlayerId))
            {
                player_spawns.Add(ev.Player.PlayerId, new Spawn());
                player_spawns[ev.Player.PlayerId].spawn_room = avaliable_spawn_rooms.First();
                avaliable_spawn_rooms.Remove(avaliable_spawn_rooms.First());
            }

            if (!round_started)
            {
                round_started = true;
                MEC.Timing.CallDelayed(1.0f, () => { Round.Start(); });
            }
            else
            {
                MEC.Timing.CallDelayed(1.0f, () => { if (!ev.Player.IsAlive) { RespawnPlayer(ev.Player); } });
            }

            if(Player.List.Where(x => x.PlayerId != 1).Count() == 1)
            {
                DmRound.GameStarted = false;
            }
            else if (Player.List.Where(x => x.PlayerId != 1).Count() == 2)
            {
                foreach (var p in Player.List)
                    if (p.IsAlive)
                        p.DisableAllEffects();
            }
        }

        // 玩家离开事件处理程序，移除玩家出生点信息并处理游戏状态
        void OnPlayerLeft(PlayerLeftEventArgs ev)
        {
            if (player_spawns.ContainsKey(ev.Player.PlayerId))
            {
                avaliable_spawn_rooms.Add(player_spawns[ev.Player.PlayerId].spawn_room);
                if (!ev.Player.DoNotTrack)
                    Database.Singleton.SaveConfigSpawn(ev.Player);
                player_spawns.Remove(ev.Player.PlayerId);
            }

            if(Player.Count == 2)
            {
                DmRound.GameStarted = false;
                foreach (var p in Player.List)
                    if (p.IsAlive)
                        ApplyGameNotStartedEffects(ev.Player);
            }
        }

        // 玩家死亡事件处理程序，结束传送协程并显示死亡信息
        void OnPlayerDeath(PlayerDyingEventArgs ev)
        {
            if (ev.Player == null)
                return;

            if (player_spawns.ContainsKey(ev.Player.PlayerId))
            {
                Timing.KillCoroutines(player_spawns[ev.Player.PlayerId].teleport_handle);
                Killfeeds.PushKill(ev.Player, ev.Attacker, ev.DamageHandler);
            }
            Killfeeds.UpdateAllDirty();
            BroadcastOverride.BroadcastLine(ev.Player, 1, 300, BroadcastPriority.Low, translation.Respawn);
            BroadcastOverride.BroadcastLine(ev.Player, 2, 300, BroadcastPriority.Low, translation.Attachments);
            BroadcastOverride.UpdateAllDirty();
        }

        // 玩家切换旁观者模式事件处理程序，处理玩家的传送和角色设置
        void OnPlayerChangeSpectator(PlayerChangedSpectatorEventArgs ev)
        {
            if (ev.Player == null || !player_spawns.ContainsKey(ev.Player.PlayerId))
                return;

            Spawn spawn = player_spawns[ev.Player.PlayerId];

            if (!spawn.in_spectator_mode)
            {
                BroadcastOverride.ClearLine(ev.Player, 1, BroadcastPriority.VeryLow);
                BroadcastOverride.ClearLine(ev.Player, 2, BroadcastPriority.VeryLow);
                BroadcastOverride.UpdateIfDirty(ev.Player);
                RespawnPlayer(ev.Player);
            }
            else
            {
                if (ev.NewTarget == null || ev.NewTarget.PlayerId == Server.Host.PlayerId)
                {
                    spawn.in_spectator_mode = false;
                    RespawnPlayer(ev.Player);
                }
            }
        }

        // 玩家切换角色事件处理程序，限制玩家的角色变化
        void OnPlayerChangeRole(PlayerChangingRoleEventArgs ev)
        {
            if (player_spawns.Count == 0)
                return;
            if (ev.Player == null || !player_spawns.ContainsKey(ev.Player.PlayerId))
            {
                ev.IsAllowed = true;
                return; 
            }
            Spawn spawn = player_spawns[ev.Player.PlayerId];
            if (ev.NewRole != spawn.role && ev.NewRole != RoleTypeId.Spectator && ev.NewRole != RoleTypeId.Overwatch && ev.NewRole != RoleTypeId.Tutorial)
            {
                Timing.CallDelayed(0.0f, () => { ev.Player.SetRole(spawn.role, RoleChangeReason.RemoteAdmin); });
                ev.IsAllowed = false;
            }
            else
            {
                ev.IsAllowed = true;
            }
        }

        // 玩家生成事件处理程序，处理玩家的出生逻辑和传送处理
        void OnPlayerSpawn(PlayerSpawningEventArgs ev)
        {
            if (ev.Player == Server.Host)
                return;
            if (!player_spawns.ContainsKey(ev.Player.PlayerId))
                return;

            Spawn spawn = player_spawns[ev.Player.PlayerId];

            if (ev.Role.RoleTypeId == spawn.role)
            {
                spawn.in_spawn = true;

                bool loadoutValid = Loadouts.ValidateLoadout(ev.Player);
                Log.Debug($"[LAB API] Player {ev.Player.Nickname} loadout validation: {loadoutValid}");

                if (!loadoutValid)
                {
                    Log.Debug($"[LAB API] Player {ev.Player.Nickname} loadout invalid, showing teleport message");
                    BroadcastOverride.BroadcastLine(ev.Player, 2, 300, BroadcastPriority.High, translation.Teleport);
                }
                else
                {
                    Log.Debug($"[LAB API] Player {ev.Player.Nickname} loadout valid, starting 7s teleport countdown");
                    BroadcastOverride.BroadcastLine(ev.Player, 1, 7, BroadcastPriority.Low, translation.Teleporting);
                    BroadcastOverride.BroadcastLine(ev.Player, 2, 7, BroadcastPriority.Low, translation.TeleportCancel);
                    Timing.KillCoroutines(spawn.teleport_handle);
                    spawn.teleport_handle = Timing.CallDelayed(7.0f, () =>
                    {
                        Log.Debug($"[LAB API] 7s teleport timer expired for {ev.Player.Nickname}");
                        if (ev.Player == null || !player_spawns.ContainsKey(ev.Player.PlayerId))
                        {
                            Log.Warn($"[LAB API] Player or spawn data missing during teleport for {ev.Player?.Nickname}");
                            return;
                        }

                        Log.Debug($"[LAB API] Player count: {Player.List.Count}, GameStarted: {DmRound.GameStarted}");

                        if (Player.List.Where(x=>x.PlayerId != 1).Count() == 1)
                        {
                            Log.Debug($"[LAB API] Only 1 player, showing waiting message for {ev.Player.Nickname}");
                            BroadcastOverride.BroadcastLines(ev.Player, 1, 1500.0f, BroadcastPriority.Low, translation.WaitingForPlayers);
                            BroadcastOverride.UpdateIfDirty(ev.Player);
                        }
                        else if (Player.List.Where(x => x.PlayerId != 1).Count() >= 2 && !DmRound.GameStarted)
                        {
                            Log.Debug($"[LAB API] 2+ players and game not started, starting game for {ev.Player.Nickname}");
                            try
                            {
                                DmRound.GameStarted = true;
                                Log.Debug($"[LAB API] Set GameStarted to true for {ev.Player.Nickname}");
                                BroadcastOverride.ClearLines(BroadcastPriority.Low);
                                Log.Debug($"[LAB API] Cleared broadcast lines for {ev.Player.Nickname}");
                                BroadcastOverride.UpdateAllDirty();
                                Log.Debug($"[LAB API] Updated all dirty broadcasts for {ev.Player.Nickname}");
                            }
                            catch (Exception ex)
                            {
                                Log.Error($"[LAB API] Exception in game start logic for {ev.Player.Nickname}: {ex}");
                            }
                        }

                        Log.Debug($"[LAB API] About to call TeleportRandom for {ev.Player.Nickname}");
                        Log.Debug($"[LAB API] Calling TeleportRandom for {ev.Player.Nickname}");
                        try
                        {
                            TeleportRandom(ev.Player);
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"[LAB API] Exception in TeleportRandom for {ev.Player.Nickname}: {ex}");
                        }
                    });
                }
                BroadcastOverride.UpdateIfDirty(ev.Player);
                Timing.CallDelayed(1.0f, () =>
                {
                    int x = spawn.spawn_room % config.SpawnDimX;
                    int y = spawn.spawn_room / config.SpawnDimY;
                    ev.Player.Position = offset + new Vector3(1.0f + x * 2.0f, 0.5f, 1.0f + y * 2.0f);
                    Log.Info("[One Spawn] Player " + ev.Player.Nickname + " spawned at " + ev.Player.Position);
                });
            }
        }

        // 波次重生事件处理程序，禁止波次重生
        void OnRespawn(WaveRespawningEventArgs _)
        {
            _.IsAllowed = false;
        }

        // 玩家逃脱事件处理程序，禁止玩家逃脱
        void OnPlayerEscape(PlayerEscapingEventArgs ev)
        {
            ev.IsAllowed = false;
        }

        // 获取玩家出生点信息方法
        public Spawn GetSpawn(Player player)
        {
            return player_spawns[player.PlayerId];
        }

        // 取消传送方法，结束传送协程
        public void CancelTeleport(Player player)
        {
            Log.Debug($"[LAB API] CancelTeleport called for {player?.Nickname}");
            Log.Debug($"[LAB API] CancelTeleport stack trace: {Environment.StackTrace}");
            if (player == null)
            {
                Log.Error("[LAB API] Player is null in CancelTeleport");
                return;
            }
            if (player_spawns.ContainsKey(player.PlayerId))
            {
                Timing.KillCoroutines(player_spawns[player.PlayerId].teleport_handle);
                Log.Debug($"[LAB API] Teleport coroutine cancelled for {player.Nickname}");
            }
            else
            {
                Log.Warn($"[LAB API] Cannot cancel teleport for {player.Nickname} - player not in spawns dictionary");
            }
        }


        // 传送出出生点方法，根据玩家状态进行传送或角色设置
        public void TeleportOutOfSpawn(Player player)
        {
            Spawn spawn = player_spawns[player.PlayerId];

            if (!Loadouts.ValidateLoadout(player))
            {
                if (spawn.in_spawn)
                    BroadcastOverride.BroadcastLine(player, 2, 300, BroadcastPriority.High, translation.Teleport);
            }
            else
            {
                if (spawn.in_spawn)
                {
                    if (player.Role == spawn.role)
                    {
                        BroadcastOverride.BroadcastLine(player, 1, 3, BroadcastPriority.VeryLow, translation.FastTeleport);
                        Timing.KillCoroutines(spawn.teleport_handle);
                        spawn.teleport_handle = Timing.CallDelayed(3.0f, () =>
                        {
                            if (Player.List.Where(x => x.PlayerId != 1).Count() == 1)
                            {
                                BroadcastOverride.BroadcastLines(player, 1, 1500.0f, BroadcastPriority.Low, translation.WaitingForPlayers);
                                BroadcastOverride.UpdateIfDirty(player);
                            }
                            else if (Player.List.Where(x => x.PlayerId != 1).Count() >= 2 && !DmRound.GameStarted)
                            {
                                DmRound.GameStarted = true;
                                BroadcastOverride.ClearLines(BroadcastPriority.Low);
                                BroadcastOverride.UpdateAllDirty();
                            }
                            TeleportRandom(player);
                        });
                    }
                    else
                        player.SetRole(spawn.role, RoleChangeReason.RemoteAdmin);
                }
                else
                {
                    if (Player.List.Where(x => x.PlayerId != 1).Count() == 1)
                    {
                        BroadcastOverride.BroadcastLines(player, 1, 1500.0f, BroadcastPriority.Low, translation.WaitingForPlayers);
                        BroadcastOverride.UpdateIfDirty(player);
                    }
                    else if (Player.List.Where(x => x.PlayerId != 1).Count() >= 2 && !DmRound.GameStarted)
                    {
                        DmRound.GameStarted = true;
                        BroadcastOverride.ClearLines(BroadcastPriority.Low);
                        BroadcastOverride.UpdateAllDirty();
                    }
                }
            }
        }

        // 检查玩家是否在出生点方法
        public bool InSpawn(Player player)
        {
            // 服务器主机（ID=1）不参与游戏逻辑，始终返回false
            if (player.PlayerId == 1)
            {
                return false;
            }

            // 检查玩家是否在spawn字典中
            if (!player_spawns.ContainsKey(player.PlayerId))
            {
                Log.Warn($"[LAB API] 玩家 {player.Nickname} (ID: {player.PlayerId}) 不在spawn字典中");
                return false;
            }

            return player_spawns[player.PlayerId].in_spawn;
        }

        // 设置玩家角色方法
        public void SetRole(Player player, RoleTypeId role)
        {
            player_spawns[player.PlayerId].role = role;
        }

        // 设置玩家旁观者模式方法
        public void SetSpectatorMode(Player player, bool is_spectator)
        {
            player_spawns[player.PlayerId].in_spectator_mode = is_spectator;
            if(is_spectator)
            {
                BroadcastOverride.ClearLines(player, BroadcastPriority.High);
                Killfeeds.SetBroadcastKillfeedLayout(player);
                BroadcastOverride.BroadcastLine(player, 1, 10, BroadcastPriority.High, translation.SpectatorMode);
                player.SetRole(RoleTypeId.Spectator);
            }
        }

        // 构建方块方法，用于构建出生点区域
        private void BuildBlock(GameObject prefab, Vector3 offset, Vector3 size)
        {
            Vector3 mid = (offset + (offset + size)) / 2.0f;
            GameObject obj = NetworkManager.Instantiate(prefab, mid, Quaternion.Euler(Vector3.zero));
            PrimitiveObjectToy toy = obj.GetComponent<PrimitiveObjectToy>();
            toy.NetworkScale = size;
            toy.NetworkPrimitiveType = PrimitiveType.Cube;
            Transform t = obj.GetComponent<Transform>();
            t.localScale = size;
            NetworkServer.Spawn(obj);
            Log.Info("built block: " + obj.name + "- Pos:" + t.position + " Scale:" + t.localScale);
            blocks.Add(obj);
        }

        // 添加光源方法，用于在出生点区域添加光源
        private void AddLight(Vector3 position, Color color, float intensity, float range)
        {
            GameObject obj = NetworkManager.Instantiate(NetworkManager.singleton.spawnPrefabs.Where((x) => x.name == "LightSourceToy").First(), position, Quaternion.Euler(Vector3.zero));
            LightSourceToy toy = obj.GetComponent<LightSourceToy>();
            toy.LightColor = color;
            toy.LightIntensity = intensity;
            toy.LightRange = range;
            toy.ShadowStrength = 0.0f;
            NetworkServer.Spawn(obj);
        }

        // 偏移向量，用于确定出生点区域的位置
        static Vector3 offset = new Vector3(42.656f, 307.25f, -47.25f);

        // 构建出生点方法，根据配置构建出生点区域
        private void BuildSpawn(int x, int y)
        {
            GameObject pot_prefab = NetworkClient.prefabs.Values.First(p => p.name == "PrimitiveObjectToy");

            BuildBlock(pot_prefab, offset, new Vector3(2.0f * x, -0.1f, 2.0f * y));
            //BuildBlock(offset + new Vector3(0.0f, 3.0f, 0.0f), new Vector3(16.0f, 0.1f, 16.0f));
            for (int i = 0; i < x + 1; i++)
            {
                BuildBlock(pot_prefab, offset + new Vector3(i * 2.0f, 0.0f, 0.0f), new Vector3(0.1f, 2.25f, 2.0f * y));
            }
            for (int i = 0; i < y + 1; i++)
            {
                BuildBlock(pot_prefab, offset + new Vector3(0.0f, 0.0f, i * 2.0f), new Vector3(2.0f * x, 2.25f, 0.1f));
            }
            AddLight(offset + new Vector3(x, x + y, y), new Color((float)config.SpawnColorRed / 255.0f, (float)config.SpawnColorGreen / 255.0f, (float)config.SpawnColorBlue / 255.0f), config.SpawnLightIntensity, (x + y) * 2.0f);
        }

        // 复活玩家方法，将玩家角色设置为出生点角色
        private void RespawnPlayer(Player player)
        {
            if (player.Role == RoleTypeId.Spectator)
            {
                player.SetRole(player_spawns[player.PlayerId].role, RoleChangeReason.Respawn);
            }
        }

        // 随机传送方法，将玩家随机传送至可用房间
        private void TeleportRandom(Player player)
        {
            if(player.PlayerId == 1)
                return;
            Log.Debug($"[LAB API] TeleportRandom method entered for {player?.Nickname ?? "null"}");
            try
            {
                if (player == null)
                {
                    Log.Error("could not teleport player because player was null");
                    return;
                }

                Log.Debug($"[LAB API] TeleportRandom: Player {player.Nickname} is not null");

                if (!player_spawns.ContainsKey(player.PlayerId))
                {
                    Log.Error("could not teleport player: " + player.Nickname + " because they where never added to players");
                    return;
                }

                Log.Debug($"[LAB API] TeleportRandom: Player {player.Nickname} found in spawns dictionary");

                Spawn spawn = player_spawns[player.PlayerId];
                Log.Debug($"[LAB API] TeleportRandom for {player.Nickname}: in_spawn={spawn.in_spawn}, role={spawn.role}");

                if (spawn.in_spawn)
                {
                    RoomIdentifier surface = RoomIdentifier.AllRoomIdentifiers.Where((r) => r.Zone == FacilityZone.Surface).FirstOrDefault();
                    if (surface == null)
                    {
                        Log.Error($"[LAB API] No surface room found for teleport of {player.Nickname}");
                        return;
                    }
                    List<Vector3> positions = Teleport.RoomPositions(surface);
                    List<bool> occupied_positions = new List<bool>(positions.Count);
                    foreach (var p in positions)
                        occupied_positions.Add(false);

                    HashSet<Room> occupied_rooms = new HashSet<Room>();
                    foreach (Player p in Player.List)
                    {
                        if (Rooms.ValidPlayerInRoom(p))
                        {
                            if (p.Room.Zone != FacilityZone.Surface)
                                occupied_rooms.Add(p.Room);
                            else
                            {
                                int i = 1;
                                int closest_index = 0;
                                float distance = Vector3.Distance(positions.First(), p.Position);
                                for (; i < positions.Count; i++)
                                {
                                    if (Vector3.Distance(positions[i], p.Position) < distance)
                                    {
                                        closest_index = i;
                                        distance = Vector3.Distance(positions[i], p.Position);
                                    }
                                }
                                occupied_positions[closest_index] = true;
                            }
                        }
                    }
                    if (occupied_positions.All((occupied) => occupied))
                        occupied_positions.Add(false);

                    HashSet<Room> occupied_adjacent_rooms = new HashSet<Room>();
                    foreach (var o in occupied_rooms)
                    {
                        occupied_adjacent_rooms.Add(o);
                        foreach (var adj in FacilityManager.GetAdjacent(o.Base).Keys)
                        {
                            var adj1 = Room.List.Where(r => r.Base == adj).FirstOrDefault();
                            if (adj1 != null)
                                occupied_adjacent_rooms.Add(adj1);
                        }
                    }

                    IEnumerable<Room> opened_rooms = Rooms.OpenedRooms;
                    Log.Debug($"[LAB API] Found {opened_rooms.Count()} opened rooms for {player.Nickname}");

                    IEnumerable<Room> available_rooms = opened_rooms.Except(occupied_adjacent_rooms);
                    Log.Debug($"[LAB API] {available_rooms.Count()} rooms available after excluding adjacent for {player.Nickname}");

                    if (available_rooms.IsEmpty())
                    {
                        available_rooms = opened_rooms.Except(occupied_rooms);
                        Log.Debug($"[LAB API] No adjacent-free rooms, trying {available_rooms.Count()} rooms excluding occupied for {player.Nickname}");
                    }
                    if (available_rooms.IsEmpty())
                    {
                        available_rooms = opened_rooms;
                        Log.Debug($"[LAB API] No available rooms, using all {opened_rooms.Count()} opened rooms for {player.Nickname}");
                    }

                    System.Random random = new System.Random();
                    Room room = null;
                    if (!available_rooms.IsEmpty())
                    {
                        // 检查过滤后的集合是否为空
                        IEnumerable<Room> filtered_rooms = available_rooms.Where(x => x.Name != RoomName.Unnamed && x.Name != RoomName.HczServers && x.Name != RoomName.Hcz096);
                        if (filtered_rooms.Any())
                        {
                            room = filtered_rooms.ElementAt(random.Next(filtered_rooms.Count()));
                        }
                        else
                        {
                            // 如果过滤后的集合为空，直接从 available_rooms 中随机选择一个房间
                            room = available_rooms.ElementAt(random.Next(available_rooms.Count()));
                            Log.Warn($"[LAB API] No rooms with name other than {RoomName.Unnamed} available for {player.Nickname}, using default teleport");
                        }
                    }
                    else
                    {
                        Log.Error("could not teleport player: " + player.Nickname + " because there was no opened rooms");
                        return;
                    }

                    if (room != null)
                    {
                        Log.Debug($"[LAB API] Teleporting {player.Nickname} to room {room.Name} in zone {room.Zone}");

                        if (room.Zone != FacilityZone.Surface)
                        {
                            Teleport.Room(player, room.Base);
                            Log.Debug($"[LAB API] Teleported {player.Nickname} to non-surface room {room.Name}");
                        }
                        else
                        {
                            List<int> indexes = new List<int>();
                            for (int i = 0; i < occupied_positions.Count; i++)
                                if (!occupied_positions[i])
                                    indexes.Add(i);
                            if (indexes.Count > 0)
                            {
                                int selectedIndex = indexes.RandomItem();
                                Teleport.RoomAt(player, room.Base, selectedIndex);
                                Log.Debug($"[LAB API] Teleported {player.Nickname} to surface room {room.Name} at position {selectedIndex}");
                            }
                            else
                            {
                                Log.Warn($"[LAB API] No available surface positions for {player.Nickname}, using default teleport");
                                Teleport.Room(player, room.Base);
                            }
                        }

                        if (DmRound.GameStarted)
                        {
                            Statistics.SetPlayerStartTime(player, Time.time);
                            Killstreaks.Singleton.AddKillstreakStartEffects(player);
                            Log.Debug($"[LAB API] Applied game started effects to {player.Nickname}");
                        }
                        else
                        {
                            ApplyGameNotStartedEffects(player);
                            Log.Debug($"[LAB API] Applied waiting effects to {player.Nickname}");
                        }

                        spawn.in_spawn = false;
                        Tracking.Singleton.PlayerSpawn(player);
                        player.EnableEffect<CustomPlayerEffects.SpawnProtected>(1, config.SpawnProtection);
                        Log.Info($"[LAB API] Successfully teleported {player.Nickname} to {room.Name} and set spawn protection");
                    }
                    else
                    {
                        Log.Error("could not teleport player: " + player.Nickname + " because there was no opened rooms");
                    }
                }
                else
                {
                    Log.Error("could not teleport player: " + player.Nickname + " because they are not in spawn");
                }
            }
            catch (Exception ex)
            {
                Log.Error("teleport error: " + ex.ToString());
            }
        }


        // 应用游戏未开始效果方法，为玩家应用移动加速和伤害减免效果
        public static void ApplyGameNotStartedEffects(Player player)
        {
            if (player != Server.Host)
            {
                player.EnableEffect<CustomPlayerEffects.MovementBoost>(255, 9999);
                player.EnableEffect<CustomPlayerEffects.DamageReduction>(255, 9999);
            }
        }

    }
}
